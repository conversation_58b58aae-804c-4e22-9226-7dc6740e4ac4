package com.joinus.knowledge.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.joinus.knowledge.model.entity.MathCatalogNodes;
import com.joinus.knowledge.model.vo.MathCatalogNodeVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【math_catalog_nodes(数学目录节点表)】的数据库操作Mapper
* @createDate 2025-07-30 17:33:40
* @Entity com.joinus.knowledge.model.entity.MathCatalogNodes
*/
@Mapper
public interface MathCatalogNodesMapper extends BaseMapper<MathCatalogNodes> {

    List<MathCatalogNodeVO> list(@Param("publisher") String publisher, @Param("grade") Integer grade, @Param("semester") Integer semester);

}




