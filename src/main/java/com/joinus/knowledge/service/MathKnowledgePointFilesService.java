package com.joinus.knowledge.service;

import com.joinus.knowledge.model.entity.MathKnowledgePointFiles;
import com.baomidou.mybatisplus.extension.service.IService;
import com.joinus.knowledge.model.param.UploadFileParam;

import java.util.List;
import java.util.UUID;

/**
* <AUTHOR>
* @description 针对表【math_knowledge_point_files(知识点文件表)】的数据库操作Service
* @createDate 2025-08-05 14:13:40
*/
public interface MathKnowledgePointFilesService extends IService<MathKnowledgePointFiles> {

    void saveKnowledgePointFileAndRelation(UUID id, List<UploadFileParam> uploadFiles);

}
