package com.joinus.knowledge.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.joinus.knowledge.model.entity.MathCatalogNodes;
import com.joinus.knowledge.model.vo.MathCatalogNodeVO;
import com.joinus.knowledge.model.vo.SimpleTreeVO;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【math_catalog_nodes(数学目录节点表)】的数据库操作Service
* @createDate 2025-07-30 17:33:40
*/
public interface MathCatalogNodesService extends IService<MathCatalogNodes> {

    List<MathCatalogNodeVO> list(String publisher, Integer grade, Integer semester);

    List<SimpleTreeVO> tree(String publisher, Integer grade, Integer semester);

}
