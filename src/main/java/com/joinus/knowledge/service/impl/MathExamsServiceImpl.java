package com.joinus.knowledge.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.joinus.knowledge.config.base.BusinessException;
import com.joinus.knowledge.enums.*;
import com.joinus.knowledge.mapper.MathExamQuestionsMapper;
import com.joinus.knowledge.mapper.MathExamsMapper;
import com.joinus.knowledge.mapper.MathQuestionsMapper;
import com.joinus.knowledge.model.dto.AddExamQuestionParam;
import com.joinus.knowledge.model.dto.SchoolExamDTO;
import com.joinus.knowledge.model.param.*;
import com.joinus.knowledge.model.entity.*;
import com.joinus.knowledge.model.po.ExamQuestionPO;
import com.joinus.knowledge.model.po.MathKnowledgePointPO;
import com.joinus.knowledge.model.vo.*;
import com.joinus.knowledge.service.*;
import com.joinus.knowledge.service.impl.tag.TagPropertyUtils;
import com.joinus.knowledge.utils.AliOssUtils;
import com.joinus.knowledge.utils.FileUtil;
import groovy.lang.Lazy;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.util.*;
import java.util.stream.Collectors;

/**
* <AUTHOR>
* @description 针对表【math_exams(试卷表)】的数据库操作Service实现
* @createDate 2025-03-20 18:15:37
*/
@Service
public class MathExamsServiceImpl extends ServiceImpl<MathExamsMapper, MathExam>
    implements MathExamsService{

    @Autowired
    private MathExamQuestionsMapper mathExamQuestionsMapper;
    @Autowired
    private MathQuestionsMapper mathQuestionsMapper;
    @Autowired
    private MathExamFilesService mathExamFilesService;
    @Autowired
    private FilesService filesService;
    @Autowired
    private AliOssUtils aliOssUtils;
    @Autowired
    private MathQuestionsService mathQuestionsService;
    @Autowired
    private QuestionFileService questionFileService;
    @Autowired
    private MathExamQuestionsService mathExamQuestionsService;
    @Lazy
    @Autowired
    private AIAbilityService aiAbilityService;
    @Autowired
    private MathExamTagsService mathExamTagsService;
    @Autowired
    private SmartStudyService smartStudyService;
    @Autowired
    private MathKnowledgePointsService mathKnowledgePointsService;

    @Override
    public List<ExamQuestionPO> listQuestionByExamId(UUID examId) {
        LambdaQueryWrapper<MathExamQuestion> examQuestionWrapper = Wrappers.lambdaQuery(MathExamQuestion.class)
                .eq(MathExamQuestion::getExamId, examId)
                .orderByAsc(MathExamQuestion::getSortNo);
        List<MathExamQuestion> mathExamQuestions = mathExamQuestionsMapper.selectList(examQuestionWrapper);

        if (CollUtil.isEmpty(mathExamQuestions)) {
            return List.of();
        }

        List<UUID> questionIdList = mathExamQuestions.stream().map(MathExamQuestion::getQuestionId).collect(Collectors.toList());
        List<ExamQuestionPO> examQuestionPOS = mathQuestionsMapper.listQuestionByIds(questionIdList);

        Map<UUID, Integer> questionSortNoMap = mathExamQuestions.stream()
                .collect(Collectors.toMap(
                        MathExamQuestion::getQuestionId,
                        MathExamQuestion::getSortNo
                ));
        examQuestionPOS.stream().forEach(item -> item.setSortNo(questionSortNoMap.getOrDefault(item.getQuestionId(), null)));
        return examQuestionPOS;
    }

    @Override
    public IPage<MathExamVO> pageQuery(Page<MathExamVO> page, MathExamPageQueryParam param) {
        if (null != param.getSchoolId()) {
            List<UUID> examIds = smartStudyService.listSchoolExamRelationBySchoolId(param.getSchoolId());
            if (CollUtil.isEmpty(examIds)) {
                return page;
            }
            param.setExamIds(examIds);
        }
        IPage<MathExamVO> result = baseMapper.pageQuery(page, param);
        List<MathExamVO> records = result.getRecords();
        if (CollUtil.isEmpty(records)) {
            return result;
        }

        List<UUID> examIdList = records.stream().map(MathExamVO::getId).toList();

        List<MathExamTag> mathExamTags = mathExamTagsService.listByExamIds(examIdList);
        Map<UUID, List<MathExamTag>> aliasTagMap = new HashMap<>();
        Map<UUID, List<MathExamTag>> eliteSchoolExamPaperTagMap = new HashMap<>();
        Map<UUID, List<MathExamTag>> regularExamTypeTagMap = new HashMap<>();
        if (CollUtil.isNotEmpty(mathExamTags)) {
            aliasTagMap = mathExamTags.stream().filter(tag -> tag.getType() == ExamTagType.ALIAS).collect(Collectors.groupingBy(tag -> tag.getExamId()));
            eliteSchoolExamPaperTagMap = mathExamTags.stream().filter(tag -> tag.getType() == ExamTagType.ELITE_SCHOOL_EXAM_PAGERS).collect(Collectors.groupingBy(tag -> tag.getExamId()));
            regularExamTypeTagMap = mathExamTags.stream().filter(tag -> tag.getType() == ExamTagType.REGULAR_EXAM_TYPE).collect(Collectors.groupingBy(tag -> tag.getExamId()));
        }
        Map<UUID, List<MathExamTag>> finalAliasTagMap = aliasTagMap;
        Map<UUID, List<MathExamTag>> finalEliteSchoolExamPaperTagMap = eliteSchoolExamPaperTagMap;
        Map<UUID, List<MathExamTag>> finalRegularExamTypeTagMap = regularExamTypeTagMap;

        List<SchoolExamDTO> schoolVOS = smartStudyService.listSchoolExamRelation(examIdList);
        Map<UUID, List<SchoolExamDTO>> schoolMap = new HashMap<>();
        if (CollUtil.isNotEmpty(schoolVOS)) {
            schoolMap = schoolVOS.stream().collect(Collectors.groupingBy(SchoolExamDTO::getExamId));
        }

        Map<UUID, List<SchoolExamDTO>> finalSchoolMap = schoolMap;
        records.forEach(item -> {
            List<MathExamQuestion> examQuestions = mathExamQuestionsService.lambdaQuery().eq(MathExamQuestion::getExamId, item.getId()).orderByAsc(MathExamQuestion::getSortNo).list();
            item.setQuestionCount(examQuestions.size());
            List<UUID> questionIds = examQuestions.stream().map(MathExamQuestion::getQuestionId).toList();
            if (CollUtil.isNotEmpty(questionIds)) {
                List<MathKnowledgePointPO> knowledgePoints = mathKnowledgePointsService.listByQuestionIds(questionIds);
                item.setPublisherTypes(knowledgePoints.stream().map(MathKnowledgePointPO::getPublisher).distinct().toList());
            } else {
                item.setPublisherTypes(List.of());
            }
            item.setAliases(finalAliasTagMap.getOrDefault(item.getId(), List.of())
                    .stream()
                    .sorted((tag1, tag2) -> {
                        boolean isPrimary1 = TagPropertyUtils.isPrimaryTag(tag1.getProperties());
                        boolean isPrimary2 = TagPropertyUtils.isPrimaryTag(tag2.getProperties());
                        // 主别名排在前面
                        return Boolean.compare(isPrimary2, isPrimary1);
                    })
                    .map(tag -> tag.getValue())
                    .toList());
            item.setEliteSchoolExamPaperValues(finalEliteSchoolExamPaperTagMap.getOrDefault(item.getId(), List.of())
                    .stream()
                    .map(tag -> tag.getValue())
                    .toList());
            item.setRegularExamType(finalRegularExamTypeTagMap.getOrDefault(item.getId(), List.of())
                    .stream()
                    .map(tag -> RegularExamType.valueOf(tag.getValue()))
                    .findFirst()
                    .orElse(null));
            item.setSchools(finalSchoolMap.getOrDefault(item.getId(), List.of()).stream().map(school -> SchoolVO.ofSchoolDTO(school)).toList());
        });
        return result;
    }

    @Override
    public List<FileVO> listExamImagesByExamId(UUID id) {
        // 1. 查询该试卷所有图片文件（按页码排序）
        List<FileVO> result = mathExamFilesService.listFilesByExamId(id);

        for (FileVO fileVO : result) {
            // 获取文件实体
            fileVO.setOssEnum(OssEnum.ofTypeAndBucket(fileVO.getOssType(), fileVO.getOssBucket()));
            fileVO.setOssUrl(aliOssUtils.generatePresignedUrl(fileVO.getOssKey()));
        }
        return result;
    }

    @Override
    public MathExamVO queryExamDetailById(UUID id) {
        MathExam mathExam = baseMapper.selectById(id);
        List<FileVO> images = listExamImagesByExamId(id);

        MathExamVO mathExamVO = MathExamVO.ofMathExam(mathExam);
        mathExamVO.setImages(images);
        if (CollUtil.isNotEmpty(images)) {
            List<FileVO> originalImages = images.stream().filter(item -> item.getExamFileType() == ExamFileType.ORIGINAL_PAPER).toList();
            List<FileVO> handwritingRemovedImages = images.stream().filter(item -> item.getExamFileType() == ExamFileType.HANDWRITING_REMOVED_PAPER).toList();
            mathExamVO.setOriginalImages(originalImages);
            mathExamVO.setHandwritingRemovedImages(handwritingRemovedImages);
        }
        mathExamVO.setSourceName(mathExam.getSource().getName());
        mathExamVO.setStateName(mathExam.getState().getDesc());
        return mathExamVO;
    }

    @Override
    public List<MathExamQuestionVO> listMathExamQuestions(UUID id) {
        // 1. 查询试卷下的所有题目详情
        List<QuestionDetailVO> questionDetails = baseMapper.listMathExamQuestionsByExamId(id);
        if (CollUtil.isEmpty(questionDetails)) {
            return List.of();
        }
        questionDetails.stream().forEach(item -> {
            if (CollUtil.isNotEmpty(item.getFiles())) {
                rebuildQuestionDetail(item);
            }
        });
        // 2. 按照题目类型分组
        Map<QuestionType, List<QuestionDetailVO>> questionsByType = questionDetails.stream()
                .collect(Collectors.groupingBy(QuestionDetailVO::getQuestionType));
        
        // 3. 记录题目类型出现的顺序，按照sortNo排序
        List<QuestionType> questionTypeOrder = Arrays.stream(QuestionType.values())
                .sorted(Comparator.comparing(QuestionType::getSortNo))
                .toList();
        
        // 4. 按照题目类型顺序和每个类型内的sortNo排序，构建结果
        List<MathExamQuestionVO> result = new ArrayList<>();
        for (QuestionType type : questionTypeOrder) {
            List<QuestionDetailVO> questionsOfType = questionsByType.get(type);
            if (CollUtil.isEmpty(questionsOfType)) {
                continue;
            }
            
            // 对每种类型的题目按照sortNo排序
            questionsOfType.sort(Comparator.comparingInt(QuestionDetailVO::getSortNo));
            
            // 创建并添加分类VO
            MathExamQuestionVO typeVO = new MathExamQuestionVO();
            typeVO.setType(type);
            typeVO.setSortNo(questionTypeOrder.indexOf(type) + 1); // 设置题型的顺序号
            typeVO.setQuestions(questionsOfType);
            typeVO.setTypeName(type.getType());

            result.add(typeVO);
        }
        
        return result;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public QuestionDetailVO addMathExamQuestion(UUID examId, AddExamQuestionParam param) {
        MathExam mathExam = baseMapper.selectById(examId);
        if (null == mathExam) {
            throw new BusinessException("试卷不存在");
        }

        //添加题目
        MathQuestion mathQuestion = MathQuestion.builder()
                .content(param.getContent())
                .questionType(param.getType())
                .difficulty(param.getDifficulty())
                .source(mathExam.getSource().getQuestionSourceType())
                .build();

        mathQuestionsService.save(mathQuestion);

        //添加Files
        if (CollUtil.isNotEmpty(param.getOriginalFiles())) {
            for (OssFileParam fileParam : param.getOriginalFiles()) {
                String fileName = FileUtil.getFileNameFromOssKey(fileParam.getOssKey());
                File file = File.builder()
                        .name(fileName)
                        .type(FileUtil.getFileExtension(fileName))
                        .mimeType(FileUtil.getMimeTypeByFileName(fileName))
                        .ossUrl(fileParam.getOssKey())
                        .ossType(fileParam.getOssEnum().getType())
                        .ossBucket(fileParam.getOssEnum().getBucket())
                        .build();
                filesService.save(file);
                questionFileService.save(QuestionFile.builder()
                        .fileId(file.getId())
                        .questionId(mathQuestion.getId())
                        .type(fileParam.getType())
                        .sortNo(fileParam.getSortNo())
                        .build());
            }
        }

        //添加试卷题目关系
        mathExamQuestionsService.save(MathExamQuestion.builder()
                        .examId(examId)
                        .questionId(mathQuestion.getId())
                        .sortNo(param.getSortNo())
                .build());

        return baseMapper.getQuestionDetailById(examId, mathQuestion.getId());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public QuestionDetailVO updateMathExamQuestion(UUID examId, UUID questionId, AddExamQuestionParam param) {
        MathExam mathExam = baseMapper.selectById(examId);
        if (null == mathExam) {
            throw new BusinessException("试卷不存在");
        }
        MathQuestion mathQuestion = mathQuestionsService.getById(questionId);
        if (null == mathQuestion) {
            throw new BusinessException("题目不存在");
        }
        MathExamQuestion mathExamQuestion = mathExamQuestionsService.getOne(examId, questionId);
        if (null == mathExamQuestion) {
            throw new BusinessException("试卷题目关系不存在");
        }

        MathQuestion updateQuestion = MathQuestion.builder()
                .id(questionId)
                .content(param.getContent())
                .questionType(param.getType())
                .difficulty(param.getDifficulty())
                .build();
        mathQuestionsService.updateById(updateQuestion);

        if (CollUtil.isNotEmpty(param.getOriginalFiles())) {
            questionFileService.removeByQuestionId(questionId);
            for (OssFileParam fileParam : param.getOriginalFiles()) {
                String fileName = FileUtil.getFileNameFromOssKey(fileParam.getOssKey());
                File file = File.builder()
                        .name(fileName)
                        .type(FileUtil.getFileExtension(fileName))
                        .mimeType(FileUtil.getMimeTypeByFileName(fileName))
                        .ossUrl(fileParam.getOssKey())
                        .ossType(fileParam.getOssEnum().getType())
                        .ossBucket(fileParam.getOssEnum().getBucket())
                        .build();
                filesService.save(file);
                questionFileService.save(QuestionFile.builder()
                        .fileId(file.getId())
                        .questionId(mathQuestion.getId())
                        .type(fileParam.getType())
                        .sortNo(fileParam.getSortNo())
                        .build());
            }
        }

        if (null != param.getSortNo() && !param.getSortNo().equals(mathExamQuestion.getSortNo())) {
            mathExamQuestionsService.updateSortNo(examId, questionId, param.getSortNo());
        }

        QuestionDetailVO questionDetailVO = baseMapper.getQuestionDetailById(examId, questionId);
        rebuildQuestionDetail(questionDetailVO);
        return questionDetailVO;
    }

    private void rebuildQuestionDetail(QuestionDetailVO questionDetailVO) {
        if (CollUtil.isNotEmpty(questionDetailVO.getFiles())) {
            questionDetailVO.getFiles().forEach(file -> {
                file.setOssUrl(aliOssUtils.generatePresignedUrl(file.getOssKey()));
                file.setOssEnum(OssEnum.ofTypeAndBucket(file.getOssType(), file.getOssBucket()));
            });
            Map<Integer, List<FileVO>> collect = questionDetailVO.getFiles().stream().collect(Collectors.groupingBy(FileVO::getType));
            questionDetailVO.setOriginalFiles(collect.getOrDefault(1, List.of()));
            questionDetailVO.setDerivativeFiles(collect.getOrDefault(0, List.of()));
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deleteMathExamQuestion(UUID examId, UUID questionId) {
        MathExam mathExam = baseMapper.selectById(examId);
        if (null == mathExam) {
            throw new BusinessException("试卷不存在");
        }
        MathQuestion mathQuestion = mathQuestionsService.getById(questionId);
        if (null == mathQuestion) {
            throw new BusinessException("题目不存在");
        }
        MathExamQuestion mathExamQuestion = mathExamQuestionsService.getOne(examId, questionId);
        if (null == mathExamQuestion) {
            throw new BusinessException("试卷题目关系不存在");
        }

        List<QuestionFile> questionFiles =questionFileService.selectByQuestionId(questionId);
        if (CollUtil.isNotEmpty(questionFiles)) {
            List<UUID> fileIds = questionFiles.stream().map(QuestionFile::getFileId).toList();
            // 使用批量删除方法
            if (CollUtil.isNotEmpty(fileIds)) {
                fileIds.stream().forEach(id -> filesService.removeById(id));
            }
            questionFileService.removeByQuestionId(questionId);
        }

        mathQuestionsService.removeById(questionId);
        mathExamQuestionsService.removeQuestion(examId, questionId);

    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public MathExamVO updateExamDetail(UUID id, UpdateMathExamParam param) {
        MathExam mathExam = baseMapper.selectById(id);
        if (null == mathExam) {
            throw new BusinessException("试卷不存在");
        }
        boolean canUpdate = checkUpdateMathExamState(mathExam, param.getState());
        if (!canUpdate) {
            throw new BusinessException("试卷本次状态不允许修改");
        }

        lambdaUpdate().eq(MathExam::getId, id)
                .set(StrUtil.isNotBlank(param.getName()), MathExam::getName, param.getName())
                .set(null != param.getSemester(), MathExam::getSemester, param.getSemester())
                .set(null != param.getGrade(), MathExam::getGrade, param.getGrade())
                .set(null != param.getPublisher(), MathExam::getPublisher, param.getPublisher())
                .set(null != param.getState(), MathExam::getState, param.getState())
                .set(null != param.getExamSectionDescriptions(), MathExam::getExamSectionDescriptions, param.getExamSectionDescriptions())
                .set(MathExam::getUpdatedAt, new Date())
                .update();

        // 更新图片
        if (CollUtil.isNotEmpty(param.getImages())) {
            mathExamFilesService.removeByExamId(id);
            List<String> ossKeys = param.getImages()
                    .stream()
                    .sorted(Comparator.comparingInt(OssFileParam::getSortNo))
                    .map(OssFileParam::getOssKey)
                    .toList();
            aiAbilityService.saveExamFileAndRelation(ossKeys, id, param.getImages().get(0).getOssEnum(), ExamFileType.ORIGINAL_PAPER);
        }
        return queryExamDetailById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public UUID createMathExam(CreateExamParam param) {
        MathExam mathExam = MathExam.builder()
                .name(param.getName())
                .grade(param.getGrade())
                .semester(param.getSemester())
                .publisher(param.getPublisher())
                .state(ExamStateEnum.DONE)
                .source(null == param.getExamSource() ? ExamSourceType.SPECIAL_TRAINING : param.getExamSource())
                .build();
        baseMapper.insert(mathExam);
        mathExamQuestionsService.createRelations(mathExam.getId(), param.getQuestionIds());
        return mathExam.getId();
    }

    @Override
    public void updateExamByExcel(List<UpdateExamByExcelParam> dataList) {
        dataList.forEach(data -> {
            MathExam mathExam = MathExam.builder()
                    .id(data.getExamId())
                    .name(data.getExamName())
                    .year(data.getYear())
                    .semester(data.getSemester())
                    .grade(data.getGrade())
                    .regionPath(data.getRegionPath())
                    .build();
            baseMapper.updateById(mathExam);
        });
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ExamStateEnum reviewMathExam(ReviewMathExamParam param) {
        MathExam mathExam = baseMapper.selectById(param.getExamId());
        Assert.isTrue(null != mathExam, "试卷不存在");
        ExamStateEnum newState = mathExam.getState().handle(param.getEvent());
        lambdaUpdate()
                .set(MathExam::getState, newState)
                .eq(MathExam::getId, mathExam.getId())
                .update();
        return newState;
    }

    @Override
    public MathExamVO updateExamInfo(UUID id, UpdateMathExamParam param) {
        MathExam mathExam = baseMapper.selectById(id);
        if (null == mathExam) {
            throw new BusinessException("试卷不存在");
        }

        //校验地区时候合法 TODO

        // 构建更新对象，使用updateById方法以确保LtreeTypeHandler正确处理regionPath字段
        MathExam updateExam = MathExam.builder()
                .id(id)
                .updatedAt(new Date())
                .build();

        // 只设置需要更新的字段
        if (StrUtil.isNotBlank(param.getName())) {
            updateExam.setName(param.getName());
        }
        if (null != param.getSemester()) {
            updateExam.setSemester(param.getSemester());
        }
        if (null != param.getGrade()) {
            updateExam.setGrade(param.getGrade());
        }
        if (null != param.getPublisher()) {
            updateExam.setPublisher(param.getPublisher());
        }
        if (null != param.getYear()) {
            updateExam.setYear(param.getYear());
        }
        if (StrUtil.isNotBlank(param.getRegionPath())) {
            updateExam.setRegionPath(param.getRegionPath());
        }

        baseMapper.updateById(updateExam);

        //处理别名标签
        mathExamTagsService.addOrUpdateAliases(id, param.getAliases());
        //处理学校关系
        if (CollUtil.isEmpty(param.getSchoolIds())) {
            smartStudyService.deleteSchoolExamRelation(id);
        } else {
            smartStudyService.addOrUpdateSchoolExamRelation(id, param.getSchoolIds());
        }
        //处理名校卷标签
        mathExamTagsService.addOrUpdateEliteSchoolExamPaperValues(id, param.getEliteSchoolExamPaperValues());
        //处理常规考试卷类型标签
        mathExamTagsService.addOrUpdateRegularExamType(id, param.getRegularExamType());
        return queryExamDetailById(id);
    }

    @Override
    public List<SchoolVO> listSchoolByExamId(UUID id) {
        List<JSONObject> schoolInfos = smartStudyService.listSchoolExamRelationByExamId(id);
        if (CollUtil.isEmpty(schoolInfos)) {
            return List.of();
        }
        return schoolInfos.stream().map(item -> SchoolVO.builder().id(item.getLong("id")).schoolName(item.getStr("schoolName")).build()).toList();
    }

    @Override
    public MathExamVO getExamTagsAndInfo(UUID id) {
        MathExamVO mathExamVO = queryExamDetailById(id);

        List<MathExamTag> tags = mathExamTagsService.listByExamId(id);
        if (CollUtil.isNotEmpty(tags)) {
            List<String> aliases = tags.stream().filter(tag -> tag.getType().equals(ExamTagType.ALIAS))
                    .sorted((tag1, tag2) -> {
                        boolean isPrimary1 = TagPropertyUtils.isPrimaryTag(tag1.getProperties());
                        boolean isPrimary2 = TagPropertyUtils.isPrimaryTag(tag2.getProperties());
                        // 主别名排在前面
                        return Boolean.compare(isPrimary2, isPrimary1);
                    })
                    .map(MathExamTag::getValue)
                    .collect(Collectors.toList());
            List<String> eliteSchoolExamPaperValues = tags.stream().filter(tag -> tag.getType().equals(ExamTagType.ELITE_SCHOOL_EXAM_PAGERS)).map(MathExamTag::getValue).collect(Collectors.toList());
            Optional<String> firstRegularExamType = tags.stream().filter(tag -> tag.getType().equals(ExamTagType.REGULAR_EXAM_TYPE)).map(MathExamTag::getValue).findFirst();


            mathExamVO.setAliases(aliases);
            mathExamVO.setEliteSchoolExamPaperValues(eliteSchoolExamPaperValues);
            mathExamVO.setRegularExamType(firstRegularExamType.isPresent() ? RegularExamType.valueOf(firstRegularExamType.get()) : null);
        }

        List<SchoolVO> schoolVOS = listSchoolByExamId(id);
        mathExamVO.setSchools(schoolVOS);

        return mathExamVO;
    }

    @Override
    public void updateExamState(UUID id, UpdateMathExamParam param) {
        MathExam mathExam = baseMapper.selectById(id);
        if (!CollUtil.toList(ExamStateEnum.DONE, ExamStateEnum.RECOMMEND_DISABLED).contains(mathExam.getState()) && ExamStateEnum.RECOMMEND_ENABLED == param.getState()) {
            throw new BusinessException("此试卷暂无法修改为推荐状态");
        }
        if (ExamStateEnum.RECOMMEND_ENABLED != mathExam.getState() && ExamStateEnum.RECOMMEND_DISABLED == param.getState()) {
            throw new BusinessException("此试卷暂无法修改为不推荐状态");
        }
        MathExam updateState = MathExam.builder()
                .id(id)
                .state(param.getState())
                .build();
        baseMapper.updateById(updateState);
    }

    private boolean checkUpdateMathExamState(MathExam mathExam, ExamStateEnum state) {
        if (null == state) {
            return true;
        }
        if  (CollUtil.toList(ExamStateEnum.HUMAN_RECOGNIZED, ExamStateEnum.DONE).contains(mathExam.getState())) {
            return false;
        }
        if (mathExam.getState().equals(state)) {
            return true;
        }
        if (mathExam.getState().equals(ExamStateEnum.AUTO_RECOGNIZED) && state.equals(ExamStateEnum.HUMAN_RECOGNIZED)) {
            return true;
        }
        if (mathExam.getState().equals(ExamStateEnum.UPLOADED) && state.equals(ExamStateEnum.HUMAN_RECOGNIZED)) {
            return true;
        }
        if (mathExam.getState().equals(ExamStateEnum.TODO) && state.equals(ExamStateEnum.HUMAN_RECOGNIZED)) {
            return true;
        }
        return false;
    }
}
