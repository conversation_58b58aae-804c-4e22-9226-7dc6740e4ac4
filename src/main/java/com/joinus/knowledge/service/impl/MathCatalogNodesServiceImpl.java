package com.joinus.knowledge.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.joinus.knowledge.mapper.MathCatalogNodesMapper;
import com.joinus.knowledge.model.entity.MathCatalogNodes;
import com.joinus.knowledge.model.vo.MathCatalogNodeVO;
import com.joinus.knowledge.model.vo.SimpleTreeVO;
import com.joinus.knowledge.service.MathCatalogNodesService;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
* <AUTHOR>
* @description 针对表【math_catalog_nodes(数学目录节点表)】的数据库操作Service实现
* @createDate 2025-07-30 17:33:40
*/
@Service
public class MathCatalogNodesServiceImpl extends ServiceImpl<MathCatalogNodesMapper, MathCatalogNodes>
    implements MathCatalogNodesService{

    @Override
    public List<MathCatalogNodeVO> list(String publisher, Integer grade, Integer semester) {
        return baseMapper.list(publisher, grade, semester);
    }

    @Override
    public List<SimpleTreeVO> tree(String publisher, Integer grade, Integer semester) {
        List<MathCatalogNodeVO> nodes = baseMapper.list(publisher, grade, semester);
        return buildTreeNonRecursive(nodes);
    }

    /**
     * 非递归方式构建树结构，时间复杂度O(n)
     * 使用HashMap进行快速节点查找和父子关系建立
     *
     * @param nodes 所有节点列表
     * @return 构建好的树结构列表（根节点列表）
     */
    private List<SimpleTreeVO> buildTreeNonRecursive(List<MathCatalogNodeVO> nodes) {
        if (nodes == null || nodes.isEmpty()) {
            return new ArrayList<>();
        }

        // 使用Map存储所有节点，key为节点ID，便于快速查找
        Map<UUID, SimpleTreeVO> nodeMap = new HashMap<>();
        List<SimpleTreeVO> rootNodes = new ArrayList<>();

        // 第一次遍历：创建所有节点对象并存入Map
        for (MathCatalogNodeVO node : nodes) {
            SimpleTreeVO treeNode = new SimpleTreeVO();
            treeNode.setId(node.getId());
            treeNode.setName(node.getName());
            treeNode.setParentId(node.getParentId());
            treeNode.setChildren(new ArrayList<>());
            nodeMap.put(node.getId(), treeNode);
        }

        // 第二次遍历：建立父子关系
        for (SimpleTreeVO treeNode : nodeMap.values()) {
            UUID parentId = treeNode.getParentId();
            if (parentId == null) {
                // 根节点（parentId为null）
                rootNodes.add(treeNode);
            } else {
                // 子节点，找到父节点并添加到父节点的children中
                SimpleTreeVO parentNode = nodeMap.get(parentId);
                if (parentNode != null) {
                    parentNode.getChildren().add(treeNode);
                }
            }
        }

        return rootNodes;
    }
}




