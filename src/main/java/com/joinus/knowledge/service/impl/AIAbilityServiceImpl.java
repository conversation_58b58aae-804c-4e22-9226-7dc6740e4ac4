package com.joinus.knowledge.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.MD5;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.joinus.knowledge.config.base.BusinessException;
import com.joinus.knowledge.enums.*;
import com.joinus.knowledge.model.dto.*;
import com.joinus.knowledge.model.entity.*;
import com.joinus.knowledge.model.param.*;
import com.joinus.knowledge.model.po.ContentPart;
import com.joinus.knowledge.model.po.ExamQuestionPO;
import com.joinus.knowledge.model.response.StreamResponse;
import com.joinus.knowledge.model.vo.*;
import com.joinus.knowledge.service.*;
import com.joinus.knowledge.util.StreamResponseCollector;
import com.joinus.knowledge.utils.*;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.prompt.ChatOptions;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.io.ClassPathResource;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.StreamUtils;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

@Slf4j
@Service
public class AIAbilityServiceImpl implements AIAbilityService {
    @Resource
    private AIChatService aiChatService;
    @Resource
    private MathKnowledgePointsService mathKnowledgePointsService;
    @Resource
    private PromptUtils promptUtils;
    @Resource
    private MathQuestionsService mathQuestionsService;
    @Resource
    private MathAnswersService mathAnswersService;
    @Resource
    private QuestionAnswerRelationsService questionAnswerRelationsService;
    @Resource
    private QuestionKnowledgePointsService questionKnowledgePointsService;
    @Resource
    private MinioUtils minioUtils;
    @Resource
    private AliOssUtils aliOssUtils;
    @Resource
    private FilesService filesService;
    @Resource
    private QuestionFileService questionFileService;
    @Lazy
    @Resource
    private MathExamsService mathExamsService;
    @Resource
    private ImageRecognizeService imageRecognizeService;
    @Resource
    private MathExamFilesService mathExamFilesService;
    @Resource
    private MathExamQuestionsService mathExamQuestionsService;
    @Resource
    private RedisTemplate<String, Object> redisTemplate;
    @Resource
    private EduMathGatewayService eduMathGatewayService;
    @Resource
    private MathExamTagsService mathExamTagsService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String solveMathQuestion(SolveQuestionParam param) {
        SolveQuestionDTO dto = getSolveQuestionDTO(param);

        String answerResult;
        if (dto.existGraphics()) {
            answerResult = solveProblemWithImage(dto.imageUrls());
        } else {
            answerResult = solveProblem(dto.questionText());
        }
        convertAndSaveAnswer(param.getQuestionId(), answerResult);
        return answerResult;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Flux<String> solveMathQuestionViaStream(SolveQuestionParam param) {
        SolveQuestionDTO dto = getSolveQuestionDTO(param);

        Flux<String> flux;
        if (dto.existGraphics()) {
            flux = solveProblemWithImageViaStream(dto.imageUrls());
        } else {
            flux = solveProblemViaStream(dto.questionText());
        }

        return saveStreamAnswer(param, flux);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public String solveMathQuestionViaContentParts(SolveQuestionParam param) {
        String answerResult = solveProblemWithContentParts(param.getQuestionText());
        convertAndSaveAnswerJson(param.getQuestionId(), answerResult);
        return answerResult;
    }

    @Override
    public Flux<String> solveMathQuestionViaContentPartsStream(UUID questionId) {
        QuestionDetailVO question = mathQuestionsService.getDetailById(questionId);
        return solveProblemWithContentPartsStream(question.getContent());
    }

    private Flux<String> saveStreamAnswer(SolveQuestionParam param, Flux<String> flux) {
        final StringBuilder fullContent = new StringBuilder();
        return flux
                .doOnNext(chunk -> {
                    // 收集每个内容块
                    StreamResponse response = JSONUtil.toBean(chunk, StreamResponse.class);
                    if (StrUtil.isNotEmpty(response.getContent())) {
                        fullContent.append(response.getContent());
                    }
                })
                .doOnComplete(() -> {
                    // 流完成时，异步处理收集的内容
                    log.info("流式响应完成，开始保存答案... ");
                    Mono.fromRunnable(() ->
                            convertAndSaveAnswer(param.getQuestionId(), fullContent.toString())
                    ).subscribeOn(Schedulers.boundedElastic()).subscribe();
                });
    }

    private SolveQuestionDTO getSolveQuestionDTO(SolveQuestionParam param) {
        List<String> imageUrls = null;
        MathQuestion question = mathQuestionsService.getById(param.getQuestionId());
        String questionText;
        boolean existGraphics;
        if (Objects.isNull(question)) {
            // 第一次解题
            if (CollUtil.isNotEmpty(param.getObjectNames())) {
                // 有图
                imageUrls = getImageUrls(param.getObjectNames(), param.getOssEnum());
                questionText = questionImageOcr(imageUrls);
                existGraphics = checkExistGraphics(questionText);
                questionFileService.saveQuestionFileAndRelation(param.getObjectNames(), param.getQuestionId(), param.getOssEnum(), QuestionFileType.ORIGIN);
            } else {
                // 无图
                questionText = param.getQuestionText();
                existGraphics = false;
            }
            String questionType = judgeQuestionType(questionText);
            saveQuestion(param.getQuestionId(), questionText, existGraphics, questionType);
        } else {
            // 非第一次
            questionText = question.getContent();
            existGraphics = question.getExistGraphics();
            imageUrls = getQuestionOriginImagesById(param.getQuestionId());
        }
        return new SolveQuestionDTO(imageUrls, questionText, existGraphics);
    }

    private Flux<String> streamSaveAnswer(SolveQuestionParam param, Flux<String> flux) {
        return StreamResponseCollector.collectFluxNonBlockingNoReasoningContent(
                flux,
                "math-question-answer" + param.getQuestionId().toString(),
                // 流结束时的异步处理函数
                answerResult -> Mono.fromRunnable(() ->
                        convertAndSaveAnswer(param.getQuestionId(), answerResult)
                ).subscribeOn(Schedulers.boundedElastic())
        );
    }

    private void convertAndSaveAnswer(UUID questionId, String result) {
        MathAnswer mathAnswer = parseAnswer(result);
        mathAnswersService.save(mathAnswer);
        questionAnswerRelationsService.createAssociation(questionId, mathAnswer.getId());
    }

    private void convertAndSaveAnswerJson(UUID questionId, String result) {
        MathAnswer mathAnswer = JSONUtil.toBean(result, MathAnswer.class);
        mathAnswersService.save(mathAnswer);
        questionAnswerRelationsService.createAssociation(questionId, mathAnswer.getId());
    }

    @Override
    public MathAnswer parseAnswer(String inputText) {
        String answer = "";
        if (inputText.contains("正确答案：")) {
            answer = inputText.split("正确答案：")[1].trim().split("\\n")[0];
        }

        String content = "";
        if (inputText.contains("解析过程：")) {
            content = inputText.substring(inputText.indexOf("解析过程：") + 5);
            content = content.trim();
        }
        MathAnswer questionAnswer = new MathAnswer();
        questionAnswer.setAnswer(answer);
        questionAnswer.setContent(content);
        return questionAnswer;
    }

    private void saveQuestion(UUID questionId, String questionText, boolean existGraphics, String questionType) {
        questionText = ConverterUtils.removeQuestionPrefix(questionText);
        MathQuestion mathQuestion = MathQuestion.builder()
                .id(questionId)
                .content(questionText)
                .questionType(QuestionType.getByType(questionType))
                .source(QuestionSourceType.USER)
                .existGraphics(existGraphics)
                .build();
        mathQuestionsService.save(mathQuestion);
    }

    private void updateQuestion(UUID questionId, String questionText, boolean existGraphics) {
        questionText = ConverterUtils.removeQuestionPrefix(questionText);
        MathQuestion mathQuestion = MathQuestion.builder()
                .id(questionId)
                .content(questionText)
                .source(QuestionSourceType.USER_EXAM)
                .existGraphics(existGraphics)
                .build();
        mathQuestionsService.updateById(mathQuestion);
    }

    @Override
    public HashMap<String, UUID> saveExamFileAndRelation(List<String> objectNames, UUID examId, OssEnum ossEnum, ExamFileType examFileType) {
        examFileType = null == examFileType ? ExamFileType.ORIGINAL_PAPER : examFileType;
        HashMap<String, UUID> fileMap = new HashMap<>();
        for (int i = 0; i < objectNames.size(); i++) {
            String objectName = objectNames.get(i);
            String originalImageType = objectName.endsWith(".jpg") ? "jpg" : "png";
            String originalMimeType = originalImageType.equals("jpg") ? "image/jpeg" : "image/png";
            com.joinus.knowledge.model.entity.File file = filesService.save(objectName.substring(objectName.lastIndexOf("/") + 1), originalImageType, originalMimeType, objectName, ossEnum);
            MathExamFiles examFile = new MathExamFiles();
            examFile.setExamId(examId);
            examFile.setFileId(file.getId());
            examFile.setSortNo(i);
            examFile.setType(examFileType);
            mathExamFilesService.save(examFile);
            fileMap.put(objectName, examFile.getFileId());
        }
        return fileMap;
    }

    public List<String> getImageUrls(List<String> objectNames, OssEnum ossEnum) {
        return objectNames.stream().map(objectName -> {
            String imageUrl = null;
            switch (ossEnum) {
                case OssEnum.MINIO_EDU_KNOWLEDGE_HUB ->
                        imageUrl = minioUtils.getPresignedObjectUrl(OssEnum.MINIO_EDU_KNOWLEDGE_HUB.getBucket(), objectName);
                case OssEnum.ALIYUN_EDU_KNOWLEDGE_HUB ->
                        imageUrl = aliOssUtils.generatePresignedUrl(OssEnum.ALIYUN_EDU_KNOWLEDGE_HUB.getBucket(), objectName);
            }
            return imageUrl;
        }).filter(Objects::nonNull).toList();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public KnowledgePointsAndDifficultyVO getProblemKnowledgePoints(GenerateKnowledgePointParam param) {
        MathQuestion mathQuestion = mathQuestionsService.getById(param.getQuestionId());
        Assert.isTrue(mathQuestion != null, "未找到此题目");

        if (null != param.getFromExam() && param.getFromExam()) {
            return getProblemKnowledgePointsViaDB(mathQuestion, param.getGrade(), param.getSemester(), param.getPublisher());
        } else {
            KnowledgePointsAndDifficultyVO problemKnowledgePointsViaDB = getProblemKnowledgePointsViaDB(mathQuestion, param.getGrade(), param.getSemester(), param.getPublisher());
            if (null != problemKnowledgePointsViaDB && CollUtil.isNotEmpty(problemKnowledgePointsViaDB.getKnowledgePoints())) {
                return problemKnowledgePointsViaDB;
            }
            return getProblemKnowledgePointsViaAI(mathQuestion, param.getGrade(), param.getSemester(), param.getPublisher());
        }
    }

    @Override
    public KnowledgePointsAndDifficultyVO getProblemKnowledgePointsViaDB(MathQuestion mathQuestion, Integer grade, Integer semester, PublisherType publisher) {
        List<MathKnowledgePointVO> kps = mathKnowledgePointsService.listKnowledgePointByQuestionId(mathQuestion.getId(), grade, semester, publisher);
        KnowledgePointsAndDifficultyVO vo = new KnowledgePointsAndDifficultyVO();
        vo.setDifficulty(null == mathQuestion.getDifficulty() ? "1" : mathQuestion.getDifficulty().toString());
        if (CollUtil.isNotEmpty(kps)) {
            vo.setKnowledgePoints(kps);
        }
        return vo;
    }

    @Override
    public KnowledgePointsAndDifficultyVO getProblemKnowledgePointsViaAI(MathQuestion mathQuestion, Integer grade, Integer semester, PublisherType publisher) {
        return getProblemKnowledgePointsViaAI(mathQuestion, grade, semester, publisher, 1);
    }

    @Override
    public KnowledgePointsAndDifficultyVO getProblemKnowledgePointsViaAI(MathQuestion mathQuestion, Integer grade, Integer semester, PublisherType publisher, int attempt) {
        String promptTextTemplate = promptUtils.getPromptTemplate(PromptEnum.ANALYZE_KNOWLEDGE_POINTS);

        List<MathKnowledgePoint> knowledgePoints = mathKnowledgePointsService.listByGradeAndSemester(grade, semester, publisher);

        Map<String, List<MathKnowledgePoint>> collect = knowledgePoints.stream().collect(Collectors.groupingBy(MathKnowledgePoint::getName));

        List<String> knowledgePointNameList = knowledgePoints.stream().map(MathKnowledgePoint::getName).toList();

        List<ImageData> originImageDataList = getQuestionOriginImageDataById(mathQuestion.getId());

        QuestionAnswerDetailVO answer = getLastAnswer(mathQuestion.getId());

        String questionContent = CollUtil.isNotEmpty(originImageDataList) ? ImageTagExtractor.buildImageTagHtml(originImageDataList) : mathQuestionsService.decodeContentV2(mathQuestion.getContent());

        String promptText = StrUtil.format(promptTextTemplate, questionContent, answer.getAnswer(), answer.getContent(), knowledgePointNameList);

        List<ContentPart> contentParts = ConverterUtils.parseContent(promptText);

        JsonNode jsonSchema = JsonSchemaCache.getSchema(KnowledgePointsAndDifficultyDTO.class);

        enrichGetKnowledgePointsJsonSchema(jsonSchema,knowledgePointNameList);

        String assistantMessage;

        if (attempt < 3) {
            assistantMessage = aiChatService.chatWithContentPart(contentParts, AIModelType.DOUBAO_SEED_1_6, ChatOptions.builder().temperature(0D).build(), jsonSchema, ModelThinkingType.DISABLED);
        } else {
            assistantMessage = aiChatService.chatWithContentPart(contentParts, AIModelType.DOUBAO_SEED_1_6, ChatOptions.builder().temperature(0D).build(), jsonSchema, ModelThinkingType.ENABLED);
        }
        String jsonStr = ConverterUtils.convertToJsonStr(assistantMessage);

        KnowledgePointsAndDifficultyDTO dto = JSONUtil.toBean(jsonStr, KnowledgePointsAndDifficultyDTO.class);
        KnowledgePointsAndDifficultyVO vo = new KnowledgePointsAndDifficultyVO();
        if (null != dto) {
            if (CollUtil.isNotEmpty(dto.getKnowledgePoints())) {
                List<MathKnowledgePointVO> mathKnowledgePoints = dto.getKnowledgePoints().stream()
                        .filter(knowledgePointName -> CollUtil.isNotEmpty(collect.get(knowledgePointName)))
                        .flatMap(knowledgePointName -> collect.get(knowledgePointName).stream()
                                .map(mathKnowledgePoint -> BeanUtil.copyProperties(mathKnowledgePoint, MathKnowledgePointVO.class)))
                        .toList();
                vo.setKnowledgePoints(mathKnowledgePoints);
            }
            vo.setDifficulty(null == dto.getDifficulty() ? "1" : dto.getDifficulty().toString());
        }
        updateDifficultyAndKnowledgePointsRelation(mathQuestion, vo);
        return vo;
    }

    private void enrichGetKnowledgePointsJsonSchema(JsonNode jsonSchema, List<String> knowledgePointNameList) {
        ObjectMapper objectMapper = new ObjectMapper();

        JsonNode propertiesNode = jsonSchema.get("properties");
        if (propertiesNode instanceof ObjectNode) {
            ObjectNode difficultyNode = (ObjectNode) propertiesNode.get("difficulty");
            if (difficultyNode != null) {
                ArrayNode enumArray = objectMapper.createArrayNode();
                List<Integer> enumValues = Arrays.asList(1, 2, 3, 4, 5);
                for (Integer value : enumValues) {
                    enumArray.add(value);
                }
                difficultyNode.set("enum", enumArray);
            }
            ObjectNode knowledgePoints = (ObjectNode) propertiesNode.get("knowledgePoints");
            ObjectNode itemsNode = (ObjectNode) knowledgePoints.get("items");
            if (itemsNode != null) {
                ArrayNode knowledgePointArray = objectMapper.createArrayNode();
                for (String knowledgePointName : knowledgePointNameList) {
                    knowledgePointArray.add(knowledgePointName);
                }
                itemsNode.set("enum", knowledgePointArray);
            }
        }
    }

    private QuestionAnswerDetailVO getLastAnswer(UUID questionId) {
        UUID answerId = questionAnswerRelationsService.lambdaQuery()
                .eq(QuestionAnswerRelation::getQuestionId, questionId)
                .orderByDesc(QuestionAnswerRelation::getCreatedAt)
                .last("limit 1")
                .list()
                .stream()
                .map(QuestionAnswerRelation::getAnswerId)
                .findFirst()
                .orElse(null);

        QuestionAnswerDetailVO answer = new QuestionAnswerDetailVO();
        if (answerId != null) {
            answer = mathAnswersService.getById(answerId);
        }
        return answer;
    }

    private void updateDifficultyAndKnowledgePointsRelation(MathQuestion mathQuestion, KnowledgePointsAndDifficultyVO vo) {
        MathQuestion uQuestion = MathQuestion.builder()
                .id(mathQuestion.getId())
                .difficulty(Integer.valueOf(vo.getDifficulty()))
                .build();
        mathQuestionsService.updateById(uQuestion);
        if (CollUtil.isEmpty(vo.getKnowledgePoints())) {
            return;
        }
        questionKnowledgePointsService.batchCreateAssociationsByQuestionId(mathQuestion.getId(), vo.getKnowledgePoints().stream().map(MathKnowledgePointVO::getId).toList());
    }

    @Override
    public List<String> getQuestionOriginImagesById(UUID questionId) {
        List<com.joinus.knowledge.model.entity.File> files = filesService.listQuestionOriginImages(questionId);
        if (CollUtil.isEmpty(files)) {
            return Collections.emptyList();
        }
        return files.stream().map(file -> {
            OssEnum ossType = OssEnum.ofTypeAndBucket(file.getOssType(), file.getOssBucket());
            return filesService.getOssUrl(file.getOssUrl(), ossType);
        }).toList();
    }

    @Override
    public List<ImageData> getQuestionOriginImageDataById(UUID questionId) {
        List<com.joinus.knowledge.model.entity.File> files = filesService.listQuestionOriginImages(questionId);
        if (CollUtil.isEmpty(files)) {
            return Collections.emptyList();
        }
        return files.stream().map(file -> {
            OssEnum ossType = OssEnum.ofTypeAndBucket(file.getOssType(), file.getOssBucket());
            return new ImageData(ossType, file.getOssUrl(), filesService.getOssUrl(file.getOssUrl(), ossType));
        }).toList();
    }

    private String questionImageOcr(List<String> imageUrls) {
        String promptText = promptUtils.getPromptTemplate(PromptEnum.ANALYZE_QUESTION);
        return aiChatService.chat(promptText, imageUrls, AIModelType.DOUBAO_VISION_PRO);
    }

    @Override
    public Boolean checkExistGraphics(String question) {
        String promptTemplate = promptUtils.getPromptTemplate(PromptEnum.CHECK_IMAGE_EXISTS);
        String promptText = StrUtil.format(promptTemplate, question);
        return "是".equals(aiChatService.chat(promptText, AIModelType.DOUBAO_LITE));
    }

    @Transactional
    @Override
    public UUID getSimilarQuestionId(UUID questionId) {
        UUID similarQuestionId = questionKnowledgePointsService.getSimilarQuestion(questionId);
        /*if (similarQuestionId == null) {
            similarQuestionId = this.generateQuestion(questionId);
        }*/
        return similarQuestionId;
    }

    public String judgeQuestionType(String question) {
        String promptTemplate = promptUtils.getPromptTemplate(PromptEnum.JUDGE_QUESTION_TYPE);
        String promptText = StrUtil.format(promptTemplate, question, Arrays.stream(QuestionType.values()).map(QuestionType::getType).toArray());
        return aiChatService.chat(promptText, AIModelType.DOUBAO_LITE);
    }

    private String solveProblemWithImage(List<String> imageUrls) {
        String promptText = promptUtils.getPromptTemplate(PromptEnum.SOLVE_MATH_PROBLEM_WITH_IMAGE);
        return aiChatService.chat(promptText, imageUrls, AIModelType.DOUBAO_SEED_1_6, ChatOptions.builder().temperature(0D).build());
    }

    private String solveProblem(String ocrResult) {
        String promptTemplate = promptUtils.getPromptTemplate(PromptEnum.SOLVE_MATH_PROBLEM);
        String promptText = StrUtil.format(promptTemplate, ocrResult);
        return aiChatService.chat(promptText, AIModelType.DOUBAO_DEEPSEEK_R1, ChatOptions.builder().temperature(0D).build());
    }

    private String solveProblemWithContentParts(String content) {
        String promptTemplate = promptUtils.getPromptTemplate(PromptEnum.SOLVE_MATH_PROBLEM_WITH_JSON_SCHEMA);
        String promptText = StrUtil.format(promptTemplate, content);
        List<ContentPart> contentParts = ConverterUtils.parseContent(promptText);
        JsonNode jsonSchema = JsonSchemaCache.getSchema(MathAnswerDTO.class);
        this.enrichAnswerJsonSchemaWithDescription(jsonSchema);
        return aiChatService.chatWithContentPart(contentParts, AIModelType.DOUBAO_SEED_1_6, ChatOptions.builder().temperature(0D).build(), jsonSchema, ModelThinkingType.ENABLED);
    }

    private Flux<String> solveProblemWithContentPartsStream(String content) {
        String promptTemplate = promptUtils.getPromptTemplate(PromptEnum.SOLVE_MATH_PROBLEM);
        String promptText = StrUtil.format(promptTemplate, content);
        List<ContentPart> contentParts = ConverterUtils.parseContent(promptText);
        return aiChatService.chatStreamWithContentPart(contentParts, AIModelType.DOUBAO_SEED_1_6, ChatOptions.builder().temperature(0D).build(), null, ModelThinkingType.ENABLED);
    }

    private void enrichAnswerJsonSchemaWithDescription(JsonNode jsonSchema) {
        JsonNode propertiesNode = jsonSchema.get("properties");
        if (propertiesNode instanceof ObjectNode) {
            ObjectNode answerNode = (ObjectNode) propertiesNode.get("answer");
            if (answerNode != null) {
                answerNode.put("description", "答案内容，如果包含LaTex公式必须用美元符号包裹");
            }
            ObjectNode contentNode = (ObjectNode) propertiesNode.get("content");
            if (contentNode != null) {
                contentNode.put("description", "详细解题步骤，包括所有中间步骤，如果包含LaTex公式必须用美元符号包裹");
            }
        }
    }

    private Flux<String> solveProblemWithImageViaStream(List<String> imageUrls) {
        String promptText = promptUtils.getPromptTemplate(PromptEnum.SOLVE_MATH_PROBLEM_WITH_IMAGE);
        return aiChatService.chatStream(promptText, imageUrls, AIModelType.DOUBAO_SEED_1_6, ChatOptions.builder().temperature(0D).build());
    }

    private Flux<String> solveProblemViaStream(String ocrResult) {
        String promptTemplate = promptUtils.getPromptTemplate(PromptEnum.SOLVE_MATH_PROBLEM);
        String promptText = StrUtil.format(promptTemplate, ocrResult);
        return aiChatService.chatStream(promptText, AIModelType.DOUBAO_DEEPSEEK_R1, ChatOptions.builder().temperature(0D).build());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public MathTrainingHtmlDTO mathSpecialTraining(SpecialTrainingParam param) {
        // 构建缓存key
        UUID pdfUUID = null == param.getPdfUUID() ? UUID.randomUUID() : param.getPdfUUID();
        String cacheKey = StrUtil.format("EDU-KNOWLEDGE-HUB:MATH-TRAINING:{}:{}", param.getExamId(), pdfUUID);

        List<ExamQuestionPO> examQuestionPOS = new ArrayList<>();

        Object questions = redisTemplate.opsForValue().get(cacheKey);
        if (null != questions) {
            examQuestionPOS = JSONUtil.toList(questions.toString(), ExamQuestionPO.class);
        } else {
            // 从数据库中获取试卷题目
            examQuestionPOS = this.getQuestionIdsByKnowledgePoints(param.getKnowledgePointsIds());
        }

        log.info("examQuestionPOS {} md5 {}", JSONUtil.toJsonStr(examQuestionPOS), MD5.create().digestHex(examQuestionPOS.toString()));

        if (CollUtil.isEmpty(examQuestionPOS)) {
            throw new BusinessException("没有找到相关题目");
        }

        redisTemplate.opsForValue().setIfAbsent(cacheKey, JSONUtil.toJsonStr(examQuestionPOS), 1, TimeUnit.HOURS);

        // 处理题目中的图片，获取预签名URL
        for (ExamQuestionPO examQuestionPO : examQuestionPOS) {
            if (CollUtil.isNotEmpty(examQuestionPO.getFiles())) {
                List<FileVO> fileVOS = new ArrayList<>();
                for (File file : examQuestionPO.getFiles()) {
                    String imageUrl = aliOssUtils.generatePresignedUrl(file.getOssBucket(), file.getOssUrl());
                    fileVOS.add(FileVO.builder().id(file.getId()).ossUrl(imageUrl).build());
                }
                // 将处理后的图片列表设置回examQuestionPO
                examQuestionPO.setFileVOList(fileVOS);
            }
        }

        try {
            return null;
        } catch (Exception e) {
            log.error("生成试卷失败", e);
            return new MathTrainingHtmlDTO("生成试卷失败：" + e.getMessage(), "生成试卷失败：" + e.getMessage(), "生成试卷失败：" + e.getMessage(), "生成试卷失败：" + e.getMessage());
        }
    }


    @Override
    public String mathSpecialTrainingAnswersOnly(UUID examId) {
        // 获取试卷题目列表
        List<ExamQuestionPO> examQuestionPOS = mathExamsService.listQuestionByExamId(examId);
        if (CollUtil.isEmpty(examQuestionPOS)) {
            return "没有找到相关题目";
        }

        try {
            // 读取主模板
            String mainTemplate = readTemplateFile("static/examples/math_training_answers_only_template.html");
            // 读取答案项模板
            String answerItemTemplate = readTemplateFile("static/examples/answer_only_item_template.html");

            // 替换标题
            mainTemplate = mainTemplate.replace("<!-- EXAM_TITLE -->", "数学专项训练 - 答案");

            // 按题型分组
//            Map<String, List<ExamQuestionPO>> questionsByType = examQuestionPOS.stream()
//                    .collect(Collectors.groupingBy(ExamQuestionPO::getQuestionType));
//
//            // 构建答案内容
//            StringBuilder answersContent = new StringBuilder();
//
//            // 题型顺序（可根据需要调整）
//            List<String> typeOrder = Arrays.asList("选择题", "填空题", "计算题", "解答题", "证明题");
//
//            // 用于记录每种题型的题目数量，用于编号
//            Map<String, Integer> questionCountByType = new HashMap<>();
//
//            // 中文大写数字
//            String[] chineseNumbers = {"一", "二", "三", "四", "五", "六", "七", "八", "九", "十"};
//            int sectionIndex = 0;
//
//            // 按照预定义的顺序处理题型
//            for (String type : typeOrder) {
//                List<ExamQuestionPO> questionsOfType = questionsByType.getOrDefault(type, Collections.emptyList());
//
//                // 如果该类型没有题目，跳过
//                if (questionsOfType.isEmpty()) {
//                    continue;
//                }
//
//                // 添加题型标题（带大写中文编号）
//                String sectionTitle = "<div class=\"section-title\">" + chineseNumbers[sectionIndex] + "、" + type + "</div>";
//                answersContent.append(sectionTitle);
//
//                // 更新章节索引
//                sectionIndex++;
//
//                // 初始化该题型的题目计数
//                questionCountByType.put(type, 0);
//
//                // 处理该类型的所有题目
//                for (ExamQuestionPO question : questionsOfType) {
//                    // 更新题目计数
//                    int questionNumber = questionCountByType.get(type) + 1;
//                    questionCountByType.put(type, questionNumber);
//
//                    // 复制答案项模板
//                    String answerItemHtml = answerItemTemplate;
//
//                    // 替换答案编号
//                    answerItemHtml = answerItemHtml.replace("<!-- QUESTION_NUMBER -->", String.valueOf(questionNumber));
//
//                    // 如果有答案，则添加答案内容
//                    if (StrUtil.isNotBlank(question.getAnswer())) {
//                        // 处理答案内容，将 Markdown 和 LaTeX 转换为 HTML
//                        String processedAnswer = processQuestionContent(question.getAnswer());
//                        answerItemHtml = answerItemHtml.replace("<!-- ANSWER_CONTENT -->", processedAnswer);
//                    } else {
//                        // 如果没有答案，显示"无"
//                        answerItemHtml = answerItemHtml.replace("<!-- ANSWER_CONTENT -->", "无");
//                    }
//
//                    // 添加到答案内容中
//                    answersContent.append(answerItemHtml);
//                }
//            }
//
//            // 处理未在预定义顺序中的题型
//            for (Map.Entry<String, List<ExamQuestionPO>> entry : questionsByType.entrySet()) {
//                String type = entry.getKey();
//                List<ExamQuestionPO> questionsOfType = entry.getValue();
//
//                // 如果该类型已经处理过或没有题目，跳过
//                if (typeOrder.contains(type) || questionsOfType.isEmpty()) {
//                    continue;
//                }
//
//                // 添加题型标题（带大写中文编号）
//                String sectionTitle = "<div class=\"section-title\">" + chineseNumbers[sectionIndex] + "、" + type + "</div>";
//                answersContent.append(sectionTitle);
//
//                // 更新章节索引
//                sectionIndex++;
//
//                // 初始化该题型的题目计数
//                questionCountByType.put(type, 0);
//
//                // 处理该类型的所有题目
//                for (ExamQuestionPO question : questionsOfType) {
//                    // 更新题目计数
//                    int questionNumber = questionCountByType.get(type) + 1;
//                    questionCountByType.put(type, questionNumber);
//
//                    // 复制答案项模板
//                    String answerItemHtml = answerItemTemplate;
//
//                    // 替换答案编号
//                    answerItemHtml = answerItemHtml.replace("<!-- QUESTION_NUMBER -->", String.valueOf(questionNumber));
//
//                    // 如果有答案，则添加答案内容
//                    if (StrUtil.isNotBlank(question.getAnswer())) {
//                        // 处理答案内容，将 Markdown 和 LaTeX 转换为 HTML
//                        String processedAnswer = processQuestionContent(question.getAnswer());
//                        answerItemHtml = answerItemHtml.replace("<!-- ANSWER_CONTENT -->", processedAnswer);
//                    } else {
//                        // 如果没有答案，显示"无"
//                        answerItemHtml = answerItemHtml.replace("<!-- ANSWER_CONTENT -->", "无");
//                    }
//
//                    // 添加到答案内容中
//                    answersContent.append(answerItemHtml);
//                }
////            }
//
//            // 替换答案内容
//            mainTemplate = mainTemplate.replace("<!-- ANSWERS_CONTENT -->", answersContent.toString());

            return mainTemplate;
        } catch (Exception e) {
            log.error("读取模板文件失败", e);
            return "生成试卷失败：" + e.getMessage();
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public UUID cutExam(CutExamParam param) {
        // 获取图片链接
        List<String> imageUrls = getImageUrls(param.getObjectNames(), param.getOssEnum());
        // 识别试卷标题
        String title = getExamPaperTitle(imageUrls.getFirst());
        // 试卷入库
        MathExam mathExams = new MathExam();
        mathExams.setId(UUID.randomUUID());
        String titleTemp = ConverterUtils.removeExamPrefix(title);
        mathExams.setName(ConverterUtils.rebuildExamName(titleTemp));
        mathExams.setGrade(param.getGrade());
        mathExams.setSemester(param.getSemester());
        mathExams.setState(ExamStateEnum.TODO);
        mathExams.setPublisher(param.getPublisher());
        mathExams.setSource(ExamSourceType.USER_UPLOAD);
        mathExamsService.save(mathExams);
        // 图片和关系入库
        HashMap<String, UUID> fileMap = saveExamFileAndRelation(param.getObjectNames(), mathExams.getId(), param.getOssEnum(), ExamFileType.ORIGINAL_PAPER);
        // 切题
        List<MathQuestion> questions = new ArrayList<>();
        List<MathExamQuestion> examQuestions = new ArrayList<>();
        int maxIndex = 0;
        List<File> updateFiles = new ArrayList<>();
        for (int i = 0; i < imageUrls.size(); i++) {
            String imageUrl = imageUrls.get(i);
            CutQuestionVO cutQuestionVO = imageRecognizeService.paperStructedFromAli(imageUrl, PaperSubjectType.JUNIOR_HIGH_SCHOOL_MATH.getType());
            if (null != cutQuestionVO) {
                updateFiles.add(File.builder()
                        .id(fileMap.get(param.getObjectNames().get(i)))
                        .positions(JSONUtil.toJsonStr(cutQuestionVO))
                        .build());
            }
            for (CutQuestionSubjectDTO subject : cutQuestionVO.getSubjects()) {
                MathQuestion mathQuestion = new MathQuestion();
                mathQuestion.setId(UUID.randomUUID());
                CutExamQuestionDTO cutExamQuestionDTO = new CutExamQuestionDTO();
                cutExamQuestionDTO.setObjectName(param.getObjectNames().get(i));
                cutExamQuestionDTO.setOssEnum(param.getOssEnum());
                cutExamQuestionDTO.setPositionList(subject.getPos_list());
                mathQuestion.setContent(JSONUtil.toJsonStr(cutExamQuestionDTO));
                mathQuestion.setSource(QuestionSourceType.TODO_USER_EXAM);
                mathQuestion.setQuestionType(AliQuestionType.fromCode(subject.getType()).getQuestionType());
                if (QuestionType.OTHER.equals(mathQuestion.getQuestionType())){
                    continue;
                }
                questions.add(mathQuestion);
                MathExamQuestion mathExamQuestions = new MathExamQuestion();
                mathExamQuestions.setExamId(mathExams.getId());
                mathExamQuestions.setQuestionId(mathQuestion.getId());
                int sortNo = ++ maxIndex;
                mathExamQuestions.setSortNo(sortNo);
                examQuestions.add(mathExamQuestions);
            }
        }
        mathQuestionsService.saveBatch(questions);
        mathExamQuestionsService.saveBatch(examQuestions);
        if (CollUtil.isNotEmpty(updateFiles)) {
            filesService.updateBatchById(updateFiles);
        }
        return mathExams.getId();
    }

    @Override
    public UUID cutExamV2(CutExamParam param) {
        Assert.notNull(param.getObjectNames(), "原图图片不能为空");
        Assert.notNull(param.getHandwritingRemovedObjectNames(), "抹除笔记图片不能为空");
        if (param.getObjectNames().size() != param.getHandwritingRemovedObjectNames().size()) {
            throw new BusinessException("原图数量与抹除笔记图片数量不一致");
        }

        // 获取图片链接
        List<String> imageUrls = getImageUrls(param.getObjectNames(), param.getOssEnum());

        ExamSummaryDTO examSummaryDTO = eduMathGatewayService.summaryExam(imageUrls);
        if (null == examSummaryDTO) {
            throw new BusinessException("获取试卷题号失败");
        }
        GradeType gradeType = GradeType.ofDescription(examSummaryDTO.getGrade());
        SemesterType  semesterType = SemesterType.ofDescription(examSummaryDTO.getTerm());
        // 试卷入库
        MathExam mathExams = new MathExam();
        mathExams.setId(UUID.randomUUID());
        mathExams.setName(StrUtil.isBlank(examSummaryDTO.getTitle()) ? "无试卷名称的数学试卷" : ConverterUtils.rebuildExamName(examSummaryDTO.getTitle()));
        mathExams.setGrade(null == gradeType ? null : gradeType.getCode());
        mathExams.setSemester(null == semesterType ?  null : semesterType.getCode());
        mathExams.setState(ExamStateEnum.TODO);
        mathExams.setPublisher(param.getPublisher());
        mathExams.setSource(ExamSourceType.USER_UPLOAD);


        saveExamQuestions(examSummaryDTO, mathExams, param.getObjectNames(), param.getHandwritingRemovedObjectNames(), param.getOssEnum());
        return mathExams.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void rebuildQuestionKnowledgePoints(UUID id) {
        // 清理之前的知识点关联关系
        questionKnowledgePointsService.deleteAssociationsByQuestionId(id);
        MathQuestion question = mathQuestionsService.getById(id);
        List<MathExamQuestion> mathExamQuestions = mathExamQuestionsService.lambdaQuery()
                .eq(MathExamQuestion::getQuestionId, question.getId())
                .list();
        Integer grade = null;
        Integer semester = null;
        if (CollUtil.isNotEmpty(mathExamQuestions)) {
            MathExam mathExam = mathExamsService.getById(mathExamQuestions.getFirst().getExamId());
            grade = mathExam.getGrade();
            semester = mathExam.getSemester();
        }
        for (PublisherType publisherType : PublisherType.values()) {
            getProblemKnowledgePointsViaAI(question, grade, semester, publisherType);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveExamQuestions(ExamSummaryDTO examSummaryDTO, MathExam mathExams, List<String> objectNames, List<String> handwritingRemovedObjectNames, OssEnum ossEnum) {
        mathExamsService.save(mathExams);
        // 图片和关系入库
        saveExamFileAndRelation(objectNames, mathExams.getId(), ossEnum, ExamFileType.ORIGINAL_PAPER);
        saveExamFileAndRelation(handwritingRemovedObjectNames, mathExams.getId(), ossEnum, ExamFileType.HANDWRITING_REMOVED_PAPER);

        List<MathQuestion> questions = new ArrayList<>();
        List<MathExamQuestion> examQuestions = new ArrayList<>();
        if (CollUtil.isNotEmpty(examSummaryDTO.getQuestions())) {
            IntStream.range(0, examSummaryDTO.getQuestions().size())
                    .forEach(questionTypeNO -> {
                        ExamSummaryQuestionDTO dto = examSummaryDTO.getQuestions().get(questionTypeNO);
                        String questionTypeName = dto.getTopic();
                        IntStream.range(0, dto.getNumbers().size())
                                .forEach(i -> {
                                    Integer qNO = dto.getNumbers().get(i);
                                    MathQuestion newQuestion = MathQuestion.builder()
                                            .id(UUID.randomUUID())
                                            .source(QuestionSourceType.TODO_USER_EXAM)
                                            .questionType(QuestionType.getByType(questionTypeName))
                                            .build();
                                    questions.add(newQuestion);

                                    MathExamQuestion newRelation = MathExamQuestion.builder()
                                            .examId(mathExams.getId())
                                            .questionId(newQuestion.getId())
                                            .sortNo(qNO)
                                            .build();
                                    examQuestions.add(newRelation);
                                });

                    });
        }
        mathQuestionsService.saveBatch(questions);
        mathExamQuestionsService.saveBatch(examQuestions);
    }

    private String getExamPaperTitle(String imageUrl) {
        String promptText = promptUtils.getPromptTemplate(PromptEnum.RECOGNIZE_EXAM_PAPER_TITLE);
        return aiChatService.chat(promptText, imageUrl, AIModelType.DOUBAO_VISION_PRO);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public KnowledgePointsAndDifficultyVO saveExamQuestionAnswerAndKnowledgePoints(MathQuestion question, MathExam exam) {
        CutExamQuestionDTO cutExamQuestionDTO = JSONUtil.toBean(question.getContent(), CutExamQuestionDTO.class);
        // 根据CutExamPositionDTO中的objectName取出图片
        List<OssFileVO> fileVOList = cutExamQuestionDTO.getPositionList().stream()
                .map(cutQuestionPosDTOS -> cutQuestionImage(cutQuestionPosDTOS, cutExamQuestionDTO.getObjectName(), cutExamQuestionDTO.getOssEnum())).toList();
        // 保存图片
        for (int i = 0; i < fileVOList.size(); i++) {
            OssFileVO fileVO = fileVOList.get(i);
            String objectName = fileVO.getKey();
            String originalImageType = objectName.endsWith(".jpg") ? "jpg" : "png";
            String originalMimeType = originalImageType.equals("jpg") ? "image/jpeg" : "image/png";
            File file = filesService.save(objectName.substring(objectName.lastIndexOf("/") + 1), originalImageType, originalMimeType, objectName, fileVO.getOssEnum());

            QuestionFile questionFile = QuestionFile.builder()
                    .questionId(question.getId())
                    .fileId(file.getId())
                    .sortNo(i)
                    .type(QuestionFileType.ORIGIN.getValue())
                    .build();
            questionFileService.save(questionFile);
        }
        List<String> imageUrls = fileVOList.stream().map(OssFileVO::getPresignedUrl).toList();
        // 识别题目并更新
        String questionText = questionImageOcr(imageUrls);
        boolean existGraphics = checkExistGraphics(questionText);
        question.setExistGraphics(existGraphics);
        updateQuestion(question.getId(), questionText, existGraphics);

        SolveQuestionParam param = new SolveQuestionParam();
        param.setQuestionId(question.getId());
        param.setOssEnum(fileVOList.getFirst().getOssEnum());
        param.setQuestionText(questionText);
        solveMathQuestion(param);
        // 获取知识点
        return getProblemKnowledgePointsViaAI(question, exam.getGrade(), exam.getSemester(), exam.getPublisher());
    }

    @Override
    public void cutQuestionsFromExam(MathQuestion question) {
        if (!QuestionSourceType.TODO_USER_EXAM.equals(question.getSource())) {
            return ;
        }
        CutExamQuestionDTO cutExamQuestionDTO = JSONUtil.toBean(question.getContent(), CutExamQuestionDTO.class);
        // 根据CutExamPositionDTO中的objectName取出图片
        List<OssFileVO> fileVOList = cutExamQuestionDTO.getPositionList().stream()
                .map(cutQuestionPosDTOS -> cutQuestionImage(cutQuestionPosDTOS, cutExamQuestionDTO.getObjectName(), cutExamQuestionDTO.getOssEnum())).toList();
        // 保存图片
        for (int i = 0; i < fileVOList.size(); i++) {
            OssFileVO fileVO = fileVOList.get(i);
            String objectName = fileVO.getKey();
            String originalImageType = objectName.endsWith(".jpg") ? "jpg" : "png";
            String originalMimeType = originalImageType.equals("jpg") ? "image/jpeg" : "image/png";
            File file = filesService.save(objectName.substring(objectName.lastIndexOf("/") + 1), originalImageType, originalMimeType, objectName, fileVO.getOssEnum());

            QuestionFile questionFile = QuestionFile.builder()
                    .questionId(question.getId())
                    .fileId(file.getId())
                    .sortNo(i)
                    .type(QuestionFileType.ORIGIN.getValue())
                    .build();
            questionFileService.save(questionFile);
        }
        List<String> imageUrls = fileVOList.stream().map(OssFileVO::getPresignedUrl).toList();
        // 识别题目并更新
        String questionText = questionImageOcr(imageUrls);
        boolean existGraphics = checkExistGraphics(questionText);
        question.setExistGraphics(existGraphics);
        updateQuestion(question.getId(), questionText, existGraphics);
    }

    @Override
    public OssFileVO cutQuestionImage(List<CutQuestionPosDTO> positionDTOs, String objectName, OssEnum ossEnum) {
        CutImageFromPositionsParam cutImageFromPositionsParam = new CutImageFromPositionsParam();
        List<CoordinatePoint> positions = new ArrayList<>();
        for (int i = 0; i < positionDTOs.size(); i++) {
            CutQuestionPosDTO positionDTO = positionDTOs.get(i);
            CoordinatePoint coordinatePoint = CoordinatePoint.builder()
                    .x(positionDTO.getX())
                    .y(positionDTO.getY())
                    .position(i + 1)
                    .build();
            positions.add(coordinatePoint);
        }
        cutImageFromPositionsParam.setPositions(positions);
        cutImageFromPositionsParam.setOssKey(objectName);
        cutImageFromPositionsParam.setOssEnum(ossEnum);
        return imageRecognizeService.cutImageFromPositions(cutImageFromPositionsParam);
    }


    @Value("#{${demo.exam.map:{'a':'b','a1':'b'}}}")
    private Map<String, String> examMap;
    @Value("${exam.recommend.excludes:未明确,未定义}")
    private List<String> excludeExamNames;

    @Override
    public CheckExistExamVO checkExistExamV2(CheckExistExamParam param) {
        String firstPaperImageUrl = "";
        String objectName = param.getObjectNames().getFirst();
        switch (param.getOssEnum()) {
            case OssEnum.MINIO_EDU_KNOWLEDGE_HUB ->
                    firstPaperImageUrl = minioUtils.getPresignedObjectUrl(OssEnum.MINIO_EDU_KNOWLEDGE_HUB.getBucket(), objectName);
            case OssEnum.ALIYUN_EDU_KNOWLEDGE_HUB ->
                    firstPaperImageUrl = aliOssUtils.generatePresignedUrl(OssEnum.ALIYUN_EDU_KNOWLEDGE_HUB.getBucket(), objectName);
        }

        ExamSummaryDTO examSummaryDTO = eduMathGatewayService.summaryExam(CollUtil.toList(firstPaperImageUrl));
        if (null == examSummaryDTO) {
            throw new BusinessException("获取试卷题号失败");
        }
        String title = StrUtil.isBlank(examSummaryDTO.getTitle()) ? "无试卷名称的数学试卷" : ConverterUtils.rebuildExamName(examSummaryDTO.getTitle());

        log.info("原始 title:{}", title);
        title = ConverterUtils.rebuildExamName(title);
        log.info("去掉空格 title:{}", title);
        //额外处理匹配模拟试卷
        String titleValue = examMap.get(title);
        if (StrUtil.isNotBlank(titleValue)) {
            title = titleValue;
        }

        MathExam mathExam = getMathExamByName(title);
        if (null != mathExam) {
            return CheckExistExamVO.builder()
                    .exist(true)
                    .examId(mathExam.getId())
                    .examName(mathExam.getName())
                    .examSource(mathExam.getSource())
                    .examIds(CollUtil.toList(mathExam.getId()))
                    .build();
        }

        CheckExistExamVO checkExistExamVO = new CheckExistExamVO();
        checkExistExamVO.setExist(false);
        return checkExistExamVO;
    }

    private MathExam getMathExamByName(String title) {
        List<MathExam> list = mathExamsService.lambdaQuery()
                .eq(MathExam::getName, title)
                .eq(MathExam::getState, ExamStateEnum.DONE)
                .notIn(MathExam::getName, excludeExamNames)
                .isNotNull(MathExam::getSource)
                .in(MathExam::getSource, CollUtil.toList(ExamSourceType.USER_UPLOAD, ExamSourceType.PAST_EXAM_PAPER, ExamSourceType.REGULAR_EXAM_PAPER))
                .orderByAsc(MathExam::getCreatedAt)
                .list();

        if (CollUtil.isNotEmpty(list)) {
            return list.getFirst();
        }

        //直接查询试卷名查不到，查询试卷别名
        if (CollUtil.isEmpty(list)) {
            MathExam mathExam = mathExamTagsService.checkExistByAlias(title);
            return mathExam;
        }
        return null;
    }

    @Override
    public CheckExistExamVO checkExistExamByName(String examName) {
        MathExam mathExamByName = getMathExamByName(examName);
        if (null != mathExamByName) {
            return CheckExistExamVO.builder()
                    .exist(true)
                    .examId(mathExamByName.getId())
                    .examName(mathExamByName.getName())
                    .examSource(mathExamByName.getSource())
                    .examIds(CollUtil.toList(mathExamByName.getId()))
                    .build();
        }
        return CheckExistExamVO.builder()
                .exist(false)
                .build();
    }
    public List<ExamQuestionPO> getQuestionIdsByKnowledgePoints(List<UUID> knowledgePointIds) {
        return mathQuestionsService.getQuestionIdsByKnowledgePoints(knowledgePointIds);
    }

    /**
     * 读取模板文件
     *
     * @param templatePath 模板路径
     * @return 模板内容
     * @throws IOException 如果读取失败
     */
    private String readTemplateFile(String templatePath) throws IOException {
        ClassPathResource resource = new ClassPathResource(templatePath);
        return StreamUtils.copyToString(resource.getInputStream(), StandardCharsets.UTF_8);
    }

    @Override
    public Map<String, String> mathSpecialTrainingToPDF(SpecialTrainingParam param) {
        String html = "";
        if (!param.getSelectAnswer() && !param.getSelectQuestion()) {
            throw new RuntimeException("请选择要生成的内容");
        }
        MathTrainingHtmlDTO mathTrainingHtmlDTO = mathSpecialTraining(param);
        if (param.getSelectAnswer() && param.getSelectQuestion()) {
            html = mathTrainingHtmlDTO.getSeparateAnswersHtml();
        } else if (param.getSelectQuestion()) {
            html = mathTrainingHtmlDTO.getQuestionHtml();
        } else if (param.getSelectAnswer()) {
            html = mathTrainingHtmlDTO.getAnswerHtml();
        }
        if (StrUtil.isBlank(html) || html.equals("没有找到相关题目") || html.startsWith("生成试卷失败")) {
            throw new BusinessException("没有找到相关题目");
        }

        try {
            // 创建临时文件
            java.io.File tempFile = java.io.File.createTempFile("math_training_", ".pdf");
            log.info("创建临时文件: {}", tempFile.getAbsolutePath());

            // 在HTML中添加强制重设样式，确保使用中文字体
            String forceFontStyle = "<style>\n" +
                    "* { font-family: 'WenQuanYi Micro Hei', 'WenQuanYi Zen Hei', 'Microsoft YaHei', 'SimSun', 'SimHei', 'Noto Sans CJK SC', sans-serif !important; }\n" +
                    "body, div, p, span, h1, h2, h3, h4, h5, li, td, th {\n" +
                    "  font-family: 'WenQuanYi Micro Hei', 'WenQuanYi Zen Hei', 'Microsoft YaHei', 'SimSun', 'SimHei', 'Noto Sans CJK SC', sans-serif !important;\n" +
                    "  font-weight: normal;\n" +
                    "}\n" +
                    "svg text {\n" +
                    "  font-family: 'WenQuanYi Micro Hei', 'WenQuanYi Zen Hei', sans-serif !important;\n" +
                    "}\n" +
                    "</style>\n";

            if (html.contains("<head>")) {
                html = html.replace("<head>", "<head>\n" + forceFontStyle);
            } else {
                html = "<html><head>" + forceFontStyle + "</head><body>" + html + "</body></html>";
            }

            // 确保HTML是完整的文档
            if (!html.contains("<!DOCTYPE html>")) {
                html = "<!DOCTYPE html>\n" + html;
            }
            if (!html.contains("<html")) {
                html = "<!DOCTYPE html>\n<html>\n<head><meta charset=\"UTF-8\">" + forceFontStyle + "</head>\n<body>" + html + "</body>\n</html>";
            }

            // 将HTML内容输出到临时文件用于调试
            String debugHtmlPath = "/tmp/debug_html_" + System.currentTimeMillis() + ".html";
            try {
                java.nio.file.Files.write(java.nio.file.Paths.get(debugHtmlPath), html.getBytes(java.nio.charset.StandardCharsets.UTF_8));
                log.info("已将HTML内容输出到文件：{}", debugHtmlPath);
            } catch (Exception e) {
                log.warn("输出HTML到文件失败", e);
            }

            // 检查wkhtmltopdf是否可用
            boolean wkhtmltopdfAvailable = false;
            boolean useWrapper = false;
            try {
                // 首先检查是否有wrapper脚本
                Process checkWrapperProcess = Runtime.getRuntime().exec(new String[]{"which", "wkhtmltopdf-wrapper"});
                int wrapperExitCode = checkWrapperProcess.waitFor();
                if (wrapperExitCode == 0) {
                    wkhtmltopdfAvailable = true;
                    useWrapper = true;
                    log.info("找到wkhtmltopdf-wrapper脚本");
                } else {
                    // 如果没有wrapper脚本，检查原始的wkhtmltopdf
                    Process checkProcess = Runtime.getRuntime().exec(new String[]{"which", "wkhtmltopdf"});
                    int exitCode = checkProcess.waitFor();
                    wkhtmltopdfAvailable = (exitCode == 0);
                    log.info("wkhtmltopdf是否可用: {}", wkhtmltopdfAvailable);
                }
            } catch (Exception e) {
                log.warn("检查wkhtmltopdf可用性失败: {}", e.getMessage());
                wkhtmltopdfAvailable = false;
            }

            if (wkhtmltopdfAvailable) {
                log.info("使用{}生成PDF", useWrapper ? "wkhtmltopdf-wrapper" : "wkhtmltopdf");

                // 创建临时HTML文件供wkhtmltopdf使用
                String htmlPath = "/tmp/math_training_" + System.currentTimeMillis() + ".html";
                java.nio.file.Files.write(java.nio.file.Paths.get(htmlPath), html.getBytes(java.nio.charset.StandardCharsets.UTF_8));

                try {
                    // 构建命令
                    List<String> commandList = new ArrayList<>();
                    // 根据是否使用wrapper选择合适的命令
                    commandList.add(useWrapper ? "wkhtmltopdf-wrapper" : "wkhtmltopdf");
                    commandList.add("--enable-local-file-access");  // 允许访问本地文件
                    commandList.add("--enable-internal-links");     // 允许内部链接
                    commandList.add("--disable-smart-shrinking");   // 禁用智能缩放以提高一致性
                    // 禁用水印
                    commandList.add("--no-background");             // 禁用背景，移除水印
                    commandList.add("--no-footer-line");            // 禁用页脚线
                    commandList.add("--no-header-line");            // 禁用页眉线
                    commandList.add("--footer-center");             // 清空页脚中心内容
                    commandList.add("");                            // 页脚为空值
                    commandList.add("--header-center");             // 清空页眉中心内容
                    commandList.add("");                            // 页眉为空值
                    commandList.add("--encoding");
                    commandList.add("UTF-8");
                    commandList.add("--page-size");
                    commandList.add("A4");
                    commandList.add("--margin-top");
                    commandList.add("10mm");
                    commandList.add("--margin-right");
                    commandList.add("10mm");
                    commandList.add("--margin-bottom");
                    commandList.add("10mm");
                    commandList.add("--margin-left");
                    commandList.add("10mm");
                    // SVG嵌入支持
                    commandList.add("--image-dpi");
                    commandList.add("300");
                    commandList.add("--image-quality");
                    commandList.add("100");
                    commandList.add(htmlPath);
                    commandList.add(tempFile.getAbsolutePath());

                    // 执行命令
                    log.info("执行{}命令：{}", useWrapper ? "wkhtmltopdf-wrapper" : "wkhtmltopdf", String.join(" ", commandList));
                    Process process = Runtime.getRuntime().exec(commandList.toArray(new String[0]));

                    // 记录命令输出
                    java.io.BufferedReader reader = new java.io.BufferedReader(new java.io.InputStreamReader(process.getInputStream()));
                    String line;
                    while ((line = reader.readLine()) != null) {
                        log.info("{}输出: {}", useWrapper ? "wkhtmltopdf-wrapper" : "wkhtmltopdf", line);
                    }

                    // 记录命令错误
                    reader = new java.io.BufferedReader(new java.io.InputStreamReader(process.getErrorStream()));
                    while ((line = reader.readLine()) != null) {
                        log.warn("{}错误: {}", useWrapper ? "wkhtmltopdf-wrapper" : "wkhtmltopdf", line);
                    }

                    int exitCode = process.waitFor();

                    if (exitCode == 0) {
                        log.info("{}成功生成PDF，大小: {} 字节", useWrapper ? "wkhtmltopdf-wrapper" : "wkhtmltopdf", tempFile.length());

                        // 删除临时HTML文件
                        java.nio.file.Files.deleteIfExists(java.nio.file.Paths.get(htmlPath));
                    } else {
                        log.error("{}执行失败，退出码: {}", useWrapper ? "wkhtmltopdf-wrapper" : "wkhtmltopdf", exitCode);
                        throw new BusinessException("生成PDF失败: " + (useWrapper ? "wkhtmltopdf-wrapper" : "wkhtmltopdf") + "执行错误，退出码: " + exitCode);
                    }
                } catch (Exception e) {
                    log.error("使用{}失败", useWrapper ? "wkhtmltopdf-wrapper" : "wkhtmltopdf", e);
                    throw new BusinessException("生成PDF失败: " + e.getMessage());
                }
            } else {
                log.error("系统中未安装{}，无法生成PDF", useWrapper ? "wkhtmltopdf-wrapper" : "wkhtmltopdf");
                throw new BusinessException("生成PDF失败: 系统中未安装" + (useWrapper ? "wkhtmltopdf-wrapper" : "wkhtmltopdf"));
            }

            // 读取PDF文件内容
            byte[] pdfBytes = java.nio.file.Files.readAllBytes(tempFile.toPath());

            // 删除临时文件
            tempFile.delete();

            // 上传PDF到阿里云OSS
            String objectName = aliOssUtils.upload(pdfBytes, "math-training/pdf", UUID.randomUUID().toString() + ".pdf");

            String pdfUrl = aliOssUtils.generatePresignedUrl(OssEnum.ALIYUN_EDU_KNOWLEDGE_HUB.getBucket(), objectName);
            String pdfPreviewUrl = aliOssUtils.generatePdfPreview(OssEnum.ALIYUN_EDU_KNOWLEDGE_HUB.getBucket(), objectName);

            HashMap<String, String> map = new HashMap<>();
            map.put("pdfUrl", pdfUrl);
            map.put("pdfPreviewUrl", pdfPreviewUrl);
            return map;
        } catch (Exception e) {
            log.error("生成PDF失败", e);
            throw new BusinessException("生成PDF失败: " + e.getMessage());
        }
    }

    @Override
    public CheckExamExistKnowledgePointsVO checkExamExistKnowledgePoints(CheckExistExamKnowledgePointsParam param) {
        MathExam mathExam = mathExamsService.getById(param.getExamId());
        if (null == mathExam) {
            throw new BusinessException("试卷不存在");
        }
        if (null != mathExam.getState() && mathExam.getState().equals(ExamStateEnum.DONE)) {
            return new CheckExamExistKnowledgePointsVO(mathExam.getId(), true);
        }
        return new CheckExamExistKnowledgePointsVO(mathExam.getId(), false);
    }

    @Override
    public void getExamKnowledgePoints(GenerateKnowledgePointParam param) {
        List<ExamQuestionPO> examQuestionPOS = mathExamsService.listQuestionByExamId(param.getExamId());
        examQuestionPOS.forEach(examQuestionPO -> {
            List<UUID> knowledgePointIdsByQuestionId = questionKnowledgePointsService.getKnowledgePointIdsByQuestionId(examQuestionPO.getQuestionId());
            if (CollUtil.isNotEmpty(knowledgePointIdsByQuestionId)) {
                return;
            }

            GenerateKnowledgePointParam questionPO = GenerateKnowledgePointParam.builder()
                    .examId(param.getExamId())
                    .questionId(examQuestionPO.getQuestionId())
                    .grade(param.getGrade())
                    .semester(param.getSemester())
                    .fromExam(false)
                    .build();
            getProblemKnowledgePoints(questionPO);
        });
    }

    @Override
    public void ocrQuestionImageAndSaveContent(SolveQuestionParam param) {
        List<String> imageUrls = getImageUrls(param.getObjectNames(), param.getOssEnum());
        String questionText = questionImageOcr(imageUrls);
        boolean existGraphics = checkExistGraphics(questionText);
        updateQuestion(param.getQuestionId(), questionText, existGraphics);
    }

    @Override
    public UUID uploadMathExam(CutExamParam param) {
        // 获取图片链接
        for (int i = 0; i < param.getObjectNames().size(); i++) {

        }
        List<String> imageUrls = getImageUrls(param.getObjectNames(), param.getOssEnum());
        // 识别试卷标题
        ExamSummaryDTO examSummaryDTO = eduMathGatewayService.summaryExam(CollUtil.toList(imageUrls.getFirst()));
        if (null == examSummaryDTO) {
            throw new BusinessException("获取试卷题号失败");
        }
        String title = StrUtil.isBlank(examSummaryDTO.getTitle()) ? "无试卷名称的数学试卷" : ConverterUtils.rebuildExamName(examSummaryDTO.getTitle());
        // 试卷入库
        MathExam mathExams = new MathExam();
        mathExams.setId(UUID.randomUUID());
        String titleTemp = ConverterUtils.removeExamPrefix(title);
        mathExams.setName(ConverterUtils.rebuildExamName(titleTemp));
        mathExams.setGrade(param.getGrade());
        mathExams.setSemester(param.getSemester());
        mathExams.setState(ExamStateEnum.UPLOADED);
        mathExams.setPublisher(param.getPublisher());
        mathExamsService.save(mathExams);
        // 图片和关系入库
        saveExamFileAndRelation(param.getObjectNames(), mathExams.getId(), param.getOssEnum(), ExamFileType.ORIGINAL_PAPER);
        return mathExams.getId();
    }
}
