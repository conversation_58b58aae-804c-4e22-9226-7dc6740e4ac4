package com.joinus.knowledge.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.joinus.knowledge.config.base.BaseEntity;
import com.joinus.knowledge.config.typehandler.StringListTypeHandler;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.List;

/**
 * 
 * @TableName math_knowledge_points
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value ="math_knowledge_points")
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
public class MathKnowledgePoint extends BaseEntity {
    /**
     * 
     */
    private String name;

    /**
     * 知识点描述
     */
    private String content;

    /**
     * 知识点排序
     */
    private Integer sortNo;

    private Boolean examPoint;

    private Boolean isBase;

    private String handout;

    private String originalName;

    @TableField(typeHandler = StringListTypeHandler.class)
    private List<String> tags;
}
