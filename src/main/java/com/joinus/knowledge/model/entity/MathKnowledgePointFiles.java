package com.joinus.knowledge.model.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.joinus.knowledge.enums.KnowledgePointFileCategory;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.UUID;

/**
 * 知识点文件表
 * @TableName math_knowledge_point_files
 */
@TableName(value ="math_knowledge_point_files")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MathKnowledgePointFiles {
    /**
     * 知识点id
     */
    private UUID knowledgePointId;

    /**
     * 文件id
     */
    private UUID fileId;

    /**
     * ppt、image、video
     */
    private KnowledgePointFileCategory category;

    /**
     * 分类下排序
     */
    private Integer sortNo;

    private Date createdAt;
}