package com.joinus.knowledge.model.dto;

import com.joinus.knowledge.enums.OssEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ImageData extends MediaData {
    private int type;

    public ImageData(OssEnum dataS3Enum, String dataS3key) {
        super(dataS3Enum, dataS3key);
    }

    public ImageData(OssEnum dataS3Enum, String dataS3key, String src) {
        super(dataS3Enum, dataS3key, src);
    }
}
