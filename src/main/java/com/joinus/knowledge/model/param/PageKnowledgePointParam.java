package com.joinus.knowledge.model.param;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import org.apache.ibatis.type.ArrayTypeHandler;
import org.apache.ibatis.type.TypeHandler;

import java.util.List;
import java.util.UUID;

@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
public class PageKnowledgePointParam extends PageParam{
    private UUID id;
    private UUID textbookId;
    private String publisher;
    private Integer grade;
    private Integer semester;
    private UUID catalogNodeId;
    private String name;
    private String originalName;
    private Boolean existImage;
    private Boolean existContent;
    private Boolean existHandout;
    private Boolean existPowerPoint;
    private Boolean existVideo;
    private Boolean existPdf;
    private List<String> tags;
}
