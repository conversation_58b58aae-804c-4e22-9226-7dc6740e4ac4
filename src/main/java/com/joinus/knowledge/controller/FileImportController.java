package com.joinus.knowledge.controller;

import cn.hutool.core.util.StrUtil;
import com.joinus.knowledge.common.GlobalConstants;
import com.joinus.knowledge.common.Result;
import com.joinus.knowledge.service.FileImportService;
import com.joinus.knowledge.utils.MinioUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * 文件导入控制器
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/files")
public class FileImportController {

    private final FileImportService fileImportService;

    @Autowired
    private MinioUtils minioUtils;

    @PostMapping("/textbook/single-image/upload")
    public Result<Map<String, String>> uploadFileToOss(@RequestParam(value = "file", required = false) MultipartFile file,
                                          @RequestParam("id") UUID uuid,
                                          @RequestParam(value = "base64", required = false) String base64,
                                          @RequestParam("pageNo") Integer pageNo) {
        if ((file == null || file.isEmpty()) && StrUtil.isBlank(base64)) {
            return Result.error(400, "文件和base64不能同时为空");
        }

        if (null == uuid || null == pageNo) {
            return Result.error(400, "id和pageNo不能为空");
        }
        try {
            String ossKeyPrefix = StrUtil.format("{}/{}/{}/", uuid, pageNo, UUID.randomUUID());
            String ossKey = "";
            String fileName = "";
            boolean uploaded = false;
            if (file != null && !file.isEmpty()) {
                // 生成唯一文件名
                fileName = file.getOriginalFilename();
                ossKey = ossKeyPrefix + fileName;

                // 上传文件到MinIO
                uploaded = minioUtils.uploadInputStream(
                        GlobalConstants.MINIO_BUCKET_NAME,
                        ossKey,
                        file.getInputStream(),
                        file.getSize()
                );


            } else {
                fileName = UUID.randomUUID() + ".png";
                ossKey = ossKeyPrefix + fileName;
                uploaded = minioUtils.uploadBase64Image(GlobalConstants.MINIO_BUCKET_NAME, ossKey, base64);
            }
            if (!uploaded) {
                return Result.error(500, "文件上传失败");
            }

            
            // 获取文件URL
            log.info("文件上传成功，文件key: {}", ossKey);
            String presignedDownloadUrl = minioUtils.getPresignedDownloadUrl(GlobalConstants.MINIO_BUCKET_NAME, ossKey, fileName);

            HashMap<String, String> map = new HashMap<>();
            map.put("ossKey", ossKey);
            map.put("presignedDownloadUrl", presignedDownloadUrl);
            return Result.success(map);
        } catch (Exception e) {
            log.error("文件上传失败", e);
            return Result.error(500, "文件上传失败: " + e.getMessage());
        }
    }

    @GetMapping("/get")
    public Result<String> getByKey(@RequestParam("key") String key) {
        String imageUrl = minioUtils.getPresignedObjectUrl(GlobalConstants.MINIO_BUCKET_NAME, key);
        return Result.success(imageUrl);
    }

    @PostMapping("/upload")
    public Result<String> upload(@RequestParam(value = "file", required = false) MultipartFile file,@RequestParam("name") String name) throws IOException {
        String key = StrUtil.format("tmp/ocr/{}", name);
        minioUtils.uploadInputStream(
                GlobalConstants.MINIO_BUCKET_NAME,
                key,
                file.getInputStream(),
                file.getSize()
        );
        String imageUrl = minioUtils.getPresignedObjectUrl(GlobalConstants.MINIO_BUCKET_NAME, key);
        return Result.success(imageUrl);
    }



}
