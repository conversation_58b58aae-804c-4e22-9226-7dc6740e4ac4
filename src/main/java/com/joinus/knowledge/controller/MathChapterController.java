package com.joinus.knowledge.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.joinus.knowledge.common.Result;
import com.joinus.knowledge.config.base.BusinessException;
import com.joinus.knowledge.model.entity.MathChapter;
import com.joinus.knowledge.model.entity.MathSection;
import com.joinus.knowledge.model.param.ChapterParam;
import com.joinus.knowledge.model.param.SectionParam;
import com.joinus.knowledge.service.MathChapterService;
import com.joinus.knowledge.service.MathSectionService;
import com.joinus.knowledge.service.TextbooksService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * 数学章节管理 Controller
 */
@RestController
@RequestMapping("/math-chapters")
@RequiredArgsConstructor
public class MathChapterController {

    @Autowired
    private TextbooksService textbooksService;
    @Autowired
    private MathChapterService mathChapterService;
    @Autowired
    private MathSectionService mathSectionService;

    /**
     * 为指定教材批量添加章节和小节
     * POST /api/math-chapters/textbook/{textbookId}
     */
    @PostMapping("/textbook/{textbookId}")
    @Transactional(rollbackFor = Exception.class)
    public Result<List<Map<String, Object>>> addChaptersWithSections(
            @PathVariable("textbookId") UUID textbookId,
            @RequestBody List<ChapterParam> chaptersParams) {
        
        // 检查教材是否存在
        if (textbooksService.getById(textbookId) == null) {
            return Result.error("教材不存在");
        }
        
        List<Map<String, Object>> result = new ArrayList<>();
        
        for (ChapterParam chapterParam : chaptersParams) {
            // 验证必填字段
            if (chapterParam.getChapterName() == null || chapterParam.getChapterName().trim().isEmpty()) {
                return Result.error("章节名称不能为空");
            }
            if (chapterParam.getSortNo() == null) {
                return Result.error("章节排序号不能为空");
            }
            
            // 转换为实体对象
            MathChapter chapter = new MathChapter();
            chapter.setName(chapterParam.getChapterName());
            chapter.setSortNo(chapterParam.getSortNo());
            chapter.setTextbookId(textbookId);
            
            // 保存章节
            boolean chapterSaved = mathChapterService.save(chapter);
            if (!chapterSaved) {
                return Result.error("章节保存失败");
            }
            
            // 处理章节的小节
            List<Map<String, Object>> sectionsList = new ArrayList<>();
            List<SectionParam> sectionsParams = chapterParam.getSections();
            
            if (sectionsParams != null && !sectionsParams.isEmpty()) {
                for (SectionParam sectionParam : sectionsParams) {
                    // 验证小节必填字段
                    if (sectionParam.getSectionName() == null || sectionParam.getSectionName().trim().isEmpty()) {
                        return Result.error("小节名称不能为空");
                    }
                    if (sectionParam.getSortNo() == null) {
                        return Result.error("小节排序号不能为空");
                    }
                    
                    // 转换为实体对象
                    MathSection section = new MathSection();
                    section.setSectionName(sectionParam.getSectionName());
                    section.setSortNo(sectionParam.getSortNo());
                    section.setStartPage(sectionParam.getStartPage());
                    section.setEndPage(sectionParam.getEndPage());
                    section.setChapterId(chapter.getId());
                    
                    // 保存小节
                    boolean sectionSaved = mathSectionService.save(section);
                    if (!sectionSaved) {
                        return Result.error("小节保存失败");
                    }
                    
                    // 构建小节返回数据
                    Map<String, Object> sectionMap = new HashMap<>();
                    sectionMap.put("id", section.getId());
                    sectionMap.put("sectionName", section.getSectionName());
                    sectionMap.put("sortNo", section.getSortNo());
                    sectionMap.put("startPage", section.getStartPage());
                    sectionMap.put("endPage", section.getEndPage());
                    
                    sectionsList.add(sectionMap);
                }
            }
            
            // 构建章节返回数据
            Map<String, Object> chapterMap = new HashMap<>();
            chapterMap.put("id", chapter.getId());
            chapterMap.put("chapterName", chapter.getName());
            chapterMap.put("sortNo", chapter.getSortNo());
            chapterMap.put("sections", sectionsList);
            
            result.add(chapterMap);
        }
        
        return Result.success(result);
    }
    
    /**
     * 获取教材的所有章节及其小节
     * GET /api/math-chapters/textbook/{textbookId}
     */
    @GetMapping("/textbook/{textbookId}")
    public Result<List<Map<String, Object>>> getChaptersByTextbookId(@PathVariable("textbookId") UUID textbookId) {
        // 检查教材是否存在
        if (textbooksService.getById(textbookId) == null) {
            throw new BusinessException("教材不存在");
        }
        
        // 查询所有章节
        LambdaQueryWrapper<MathChapter> chapterWrapper = new LambdaQueryWrapper<>();
        chapterWrapper.eq(MathChapter::getTextbookId, textbookId);
        chapterWrapper.orderByAsc(MathChapter::getSortNo);
        chapterWrapper.isNull(MathChapter::getDeletedAt);
        
        List<MathChapter> chapters = mathChapterService.list(chapterWrapper);
        List<Map<String, Object>> result = new ArrayList<>();
        
        // 获取各章节的小节并转换为用户期望的JSON格式
        for (MathChapter chapter : chapters) {
            Map<String, Object> chapterMap = new HashMap<>();
            
            // 转换字段名：从name到chapterName
            chapterMap.put("id", chapter.getId());
            chapterMap.put("chapterName", chapter.getName());
            chapterMap.put("sortNo", chapter.getSortNo());
            chapterMap.put("textbookId", chapter.getTextbookId());
            
            // 查询并添加小节
            LambdaQueryWrapper<MathSection> sectionWrapper = new LambdaQueryWrapper<>();
            sectionWrapper.eq(MathSection::getChapterId, chapter.getId());
            sectionWrapper.orderByAsc(MathSection::getSortNo);
            sectionWrapper.isNull(MathSection::getDeletedAt);
            
            List<MathSection> sections = mathSectionService.list(sectionWrapper);
            List<Map<String, Object>> sectionsList = new ArrayList<>();
            
            for (MathSection section : sections) {
                Map<String, Object> sectionMap = new HashMap<>();
                sectionMap.put("id", section.getId());
                sectionMap.put("sectionName", section.getSectionName());
                sectionMap.put("sortNo", section.getSortNo());
                sectionMap.put("startPage", section.getStartPage());
                sectionMap.put("endPage", section.getEndPage());
                sectionMap.put("chapterId", section.getChapterId());
                
                sectionsList.add(sectionMap);
            }
            
            chapterMap.put("sections", sectionsList);
            result.add(chapterMap);
        }
        
        return Result.success(result);
    }
    
    /**
     * 根据ID获取章节详情及其小节
     * GET /api/math-chapters/{id}
     */
    @GetMapping("/{id}")
    public Result<Map<String, Object>> getChapterById(@PathVariable("id") UUID id) {
        MathChapter chapter = mathChapterService.getById(id);
        if (chapter == null || chapter.getDeletedAt() != null) {
            return Result.error("章节不存在或已删除");
        }
        
        // 构建章节数据
        Map<String, Object> chapterMap = new HashMap<>();
        chapterMap.put("id", chapter.getId());
        chapterMap.put("chapterName", chapter.getName());
        chapterMap.put("sortNo", chapter.getSortNo());
        chapterMap.put("textbookId", chapter.getTextbookId());
        
        // 获取章节的小节
        LambdaQueryWrapper<MathSection> sectionWrapper = new LambdaQueryWrapper<>();
        sectionWrapper.eq(MathSection::getChapterId, chapter.getId());
        sectionWrapper.orderByAsc(MathSection::getSortNo);
        sectionWrapper.isNull(MathSection::getDeletedAt);
        
        List<MathSection> sections = mathSectionService.list(sectionWrapper);
        List<Map<String, Object>> sectionsList = new ArrayList<>();
        
        for (MathSection section : sections) {
            Map<String, Object> sectionMap = new HashMap<>();
            sectionMap.put("id", section.getId());
            sectionMap.put("sectionName", section.getSectionName());
            sectionMap.put("sortNo", section.getSortNo());
            sectionMap.put("startPage", section.getStartPage());
            sectionMap.put("endPage", section.getEndPage());
            
            sectionsList.add(sectionMap);
        }
        
        chapterMap.put("sections", sectionsList);
        
        return Result.success(chapterMap);
    }
    
    /**
     * 更新章节信息
     * PUT /api/math-chapters/{id}
     */
    @PutMapping("/{id}")
    public Result<Map<String, Object>> updateChapter(@PathVariable("id") UUID id, @RequestBody ChapterParam chapterParam) {
        MathChapter existingChapter = mathChapterService.getById(id);
        if (existingChapter == null) {
            return Result.error("章节不存在");
        }
        
        // 更新章节信息
        existingChapter.setName(chapterParam.getChapterName());
        existingChapter.setSortNo(chapterParam.getSortNo());
        
        boolean success = mathChapterService.updateById(existingChapter);
        if (!success) {
            return Result.error("更新章节失败");
        }
        
        // 构建返回数据
        Map<String, Object> result = new HashMap<>();
        result.put("id", existingChapter.getId());
        result.put("chapterName", existingChapter.getName());
        result.put("sortNo", existingChapter.getSortNo());
        result.put("textbookId", existingChapter.getTextbookId());
        
        return Result.success(result);
    }
    
    /**
     * 删除章节及其所有小节
     * DELETE /api/math-chapters/{id}
     */
    @DeleteMapping("/{id}")
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> deleteChapter(@PathVariable("id") UUID id) {
        MathChapter chapter = mathChapterService.getById(id);
        if (chapter == null) {
            return Result.error("章节不存在");
        }
        
        // 逻辑删除章节
        boolean chapterDeleted = mathChapterService.removeById(id);
        if (!chapterDeleted) {
            return Result.error("删除章节失败");
        }
        
        // 查询章节下的所有小节
        LambdaQueryWrapper<MathSection> sectionWrapper = new LambdaQueryWrapper<>();
        sectionWrapper.eq(MathSection::getChapterId, id);
        
        // 逻辑删除所有小节
        boolean sectionsDeleted = mathSectionService.remove(sectionWrapper);
        if (!sectionsDeleted) {
            return Result.error("删除小节失败");
        }
        
        return Result.success();
    }
}
