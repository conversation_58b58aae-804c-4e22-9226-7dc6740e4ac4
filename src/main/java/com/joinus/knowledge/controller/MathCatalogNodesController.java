package com.joinus.knowledge.controller;

import com.joinus.knowledge.common.Result;
import com.joinus.knowledge.enums.PublisherType;
import com.joinus.knowledge.model.vo.MathCatalogNodeVO;
import com.joinus.knowledge.model.vo.MathChapterVO;
import com.joinus.knowledge.model.vo.SimpleTreeVO;
import com.joinus.knowledge.service.MathCatalogNodesService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/math/catalog/nodes")
@RequiredArgsConstructor
public class MathCatalogNodesController {

    private final MathCatalogNodesService mathCatalogNodesService;

    @GetMapping
    public Result<List<MathCatalogNodeVO>> list(@RequestParam(value = "grade", required = false) Integer grade,
                                            @RequestParam(value = "semester", required = false) Integer semester,
                                            @RequestParam(value = "publisher", required = false) String publisher) {
        List<MathCatalogNodeVO> list = mathCatalogNodesService.list(publisher, grade, semester);
        return Result.success(list);
    }

    @GetMapping("/tree")
    public Result<List<SimpleTreeVO>> tree(@RequestParam(value = "grade", required = false) Integer grade,
                                                @RequestParam(value = "semester", required = false) Integer semester,
                                                @RequestParam(value = "publisher", required = false) String publisher) {
        List<SimpleTreeVO> list = mathCatalogNodesService.tree(publisher, grade, semester);
        return Result.success(list);
    }


}
