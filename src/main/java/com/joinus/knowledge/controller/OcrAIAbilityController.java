package com.joinus.knowledge.controller;

import com.joinus.knowledge.common.Result;
import com.joinus.knowledge.model.param.*;
import com.joinus.knowledge.model.vo.*;
import com.joinus.knowledge.service.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Tag(name = "OCR AI能力接口", description = "提供文字识别相关的能力，包括通用文字识别、手写文字识别等功能")
@RestController
@AllArgsConstructor
@RequestMapping("/ai/ability/ocr")
public class OcrAIAbilityController {

    private OcrAIAbilityService ocrAIAbilityService;

    @Operation(summary = "手写文字识别", description = "通过图片URL解析用户手写文字")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "成功识别", content = @Content(mediaType = "application/json", schema = @Schema(implementation = OcrHandWritingVO.class))),
            @ApiResponse(responseCode = "400", description = "请求参数错误"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @PostMapping("/hand-writing")
    public Result<List<OcrHandWritingVO>> recognizeHandwrittenText(@RequestBody @Valid OcrHandWritingParam param) {
        List<OcrHandWritingVO> wordsResultList = ocrAIAbilityService.recognizeHandwrittenText(param);
        return Result.success(wordsResultList);
    }

}
