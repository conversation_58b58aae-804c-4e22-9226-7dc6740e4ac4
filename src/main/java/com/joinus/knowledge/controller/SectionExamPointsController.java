package com.joinus.knowledge.controller;

import com.joinus.knowledge.common.Result;
import com.joinus.knowledge.model.entity.MathExamPoints;
import com.joinus.knowledge.model.entity.MathSection;
import com.joinus.knowledge.service.MathExamPointsService;
import com.joinus.knowledge.service.MathSectionService;
import com.joinus.knowledge.service.SectionExamPointsService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * 小节-考点关联关系管理 Controller
 */
@RestController
@RequestMapping("/api/section-exam-points")
@RequiredArgsConstructor
public class SectionExamPointsController {

    private final SectionExamPointsService sectionExamPointsService;
    private final MathSectionService mathSectionService;
    private final MathExamPointsService mathExamPointsService;

    /**
     * 根据小节ID获取关联的所有考点
     * GET /api/section-exam-points/section/{sectionId}
     */
    @GetMapping("/section/{sectionId}")
    public Result<List<MathExamPoints>> getExamPointsBySectionId(@PathVariable UUID sectionId) {
        // 获取考点ID列表
        List<UUID> examPointIds = sectionExamPointsService.getExamPointIdsBySectionId(sectionId);
        
        if (examPointIds.isEmpty()) {
            return Result.success(List.of());
        }
        
        // 查询考点信息
        List<MathExamPoints> examPoints = mathExamPointsService.listByIds(examPointIds);
        return Result.success(examPoints);
    }

    /**
     * 根据考点ID获取关联的所有小节
     * GET /api/section-exam-points/exam-point/{examPointId}
     */
    @GetMapping("/exam-point/{examPointId}")
    public Result<List<MathSection>> getSectionsByExamPointId(@PathVariable UUID examPointId) {
        // 获取小节ID列表
        List<UUID> sectionIds = sectionExamPointsService.getSectionIdsByExamPointId(examPointId);
        
        if (sectionIds.isEmpty()) {
            return Result.success(List.of());
        }
        
        // 查询小节信息
        List<MathSection> sections = mathSectionService.listByIds(sectionIds);
        return Result.success(sections);
    }

    /**
     * 批量关联考点到一个小节
     * POST /api/section-exam-points/section/{sectionId}
     * Request body: ["examPointId1", "examPointId2", ...]
     */
    @PostMapping("/section/{sectionId}")
    public Result<Void> associateExamPointsWithSection(@PathVariable UUID sectionId, @RequestBody List<UUID> examPointIds) {
        boolean success = sectionExamPointsService.batchCreateAssociationsBySectionId(sectionId, examPointIds);
        return success ? Result.success() : Result.error("关联考点失败");
    }

    /**
     * 批量关联小节到一个考点
     * POST /api/section-exam-points/exam-point/{examPointId}
     * Request body: ["sectionId1", "sectionId2", ...]
     */
    @PostMapping("/exam-point/{examPointId}")
    public Result<Void> associateSectionsWithExamPoint(@PathVariable UUID examPointId, @RequestBody List<UUID> sectionIds) {
        boolean success = sectionExamPointsService.batchCreateAssociationsByExamPointId(examPointId, sectionIds);
        return success ? Result.success() : Result.error("关联小节失败");
    }

    /**
     * 创建单个关联关系
     * POST /api/section-exam-points
     * Request body: {"sectionId": "uuid", "examPointId": "uuid"}
     */
    @PostMapping
    public Result<Void> createAssociation(@RequestBody Map<String, UUID> request) {
        UUID sectionId = request.get("sectionId");
        UUID examPointId = request.get("examPointId");
        
        if (sectionId == null || examPointId == null) {
            return Result.error("小节ID和考点ID不能为空");
        }
        
        boolean success = sectionExamPointsService.createAssociation(sectionId, examPointId);
        return success ? Result.success() : Result.error("创建关联失败");
    }

    /**
     * 删除单个关联关系
     * DELETE /api/section-exam-points
     * Request body: {"sectionId": "uuid", "examPointId": "uuid"}
     */
    @DeleteMapping
    public Result<Void> deleteAssociation(@RequestBody Map<String, UUID> request) {
        UUID sectionId = request.get("sectionId");
        UUID examPointId = request.get("examPointId");
        
        if (sectionId == null || examPointId == null) {
            return Result.error("小节ID和考点ID不能为空");
        }
        
        boolean success = sectionExamPointsService.deleteAssociation(sectionId, examPointId);
        return success ? Result.success() : Result.error("删除关联失败");
    }

    /**
     * 删除小节的所有关联
     * DELETE /api/section-exam-points/section/{sectionId}
     */
    @DeleteMapping("/section/{sectionId}")
    public Result<Void> deleteAssociationsBySectionId(@PathVariable UUID sectionId) {
        boolean success = sectionExamPointsService.deleteAssociationsBySectionId(sectionId);
        return success ? Result.success() : Result.error("删除小节关联失败");
    }

    /**
     * 删除考点的所有关联
     * DELETE /api/section-exam-points/exam-point/{examPointId}
     */
    @DeleteMapping("/exam-point/{examPointId}")
    public Result<Void> deleteAssociationsByExamPointId(@PathVariable UUID examPointId) {
        boolean success = sectionExamPointsService.deleteAssociationsByExamPointId(examPointId);
        return success ? Result.success() : Result.error("删除考点关联失败");
    }
}
