package com.joinus.knowledge.controller;

import com.joinus.knowledge.common.Result;
import com.joinus.knowledge.enums.PublisherType;
import com.joinus.knowledge.model.entity.MathKnowledgePoint;
import com.joinus.knowledge.model.entity.MathQuestion;
import com.joinus.knowledge.model.vo.MathKnowledgePointVO;
import com.joinus.knowledge.service.MathKnowledgePointsService;
import com.joinus.knowledge.service.MathQuestionsService;
import com.joinus.knowledge.service.QuestionKnowledgePointsService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * 题目-知识点关联关系管理 Controller
 */
@RestController
@RequestMapping("/api/question-knowledge-points")
@RequiredArgsConstructor
public class QuestionKnowledgePointsController {

    private final QuestionKnowledgePointsService questionKnowledgePointsService;
    private final MathQuestionsService mathQuestionsService;
    private final MathKnowledgePointsService mathKnowledgePointsService;

    /**
     * 根据题目ID获取关联的所有知识点
     * GET /api/question-knowledge-points/question/{questionId}
     */
    @GetMapping("/question/{questionId}")
    public Result<List<MathKnowledgePointVO>> getKnowledgePointsByQuestionId(@PathVariable UUID questionId) {
        // 获取知识点ID列表
        List<UUID> knowledgePointIds = questionKnowledgePointsService.getKnowledgePointIdsByQuestionId(questionId);
        
        if (knowledgePointIds.isEmpty()) {
            return Result.success(List.of());
        }
        
        // 查询知识点信息
        List<MathKnowledgePointVO> knowledgePoints = mathKnowledgePointsService.listByIds(knowledgePointIds);
        return Result.success(knowledgePoints);
    }

    /**
     * 根据知识点ID获取关联的所有题目
     * GET /api/question-knowledge-points/knowledge-point/{knowledgePointId}
     */
    @GetMapping("/knowledge-point/{knowledgePointId}")
    public Result<List<MathQuestion>> getQuestionsByKnowledgePointId(@PathVariable UUID knowledgePointId) {
        // 获取题目ID列表
        List<UUID> questionIds = questionKnowledgePointsService.listQuestionIdsByKnowledgePointId(knowledgePointId);
        
        if (questionIds.isEmpty()) {
            return Result.success(List.of());
        }
        
        // 查询题目信息
        List<MathQuestion> questions = mathQuestionsService.listByIds(questionIds);
        return Result.success(questions);
    }

    /**
     * 批量关联知识点到一个题目
     * POST /api/question-knowledge-points/question/{questionId}
     * Request body: ["knowledgePointId1", "knowledgePointId2", ...]
     */
    @PostMapping("/question/{questionId}")
    public Result<Void> associateKnowledgePointsWithQuestion(@PathVariable UUID questionId, @RequestBody List<UUID> knowledgePointIds) {
        boolean success = questionKnowledgePointsService.batchCreateAssociationsByQuestionId(questionId, knowledgePointIds);
        return success ? Result.success() : Result.error("关联知识点失败");
    }

    /**
     * 批量关联题目到一个知识点
     * POST /api/question-knowledge-points/knowledge-point/{knowledgePointId}
     * Request body: ["questionId1", "questionId2", ...]
     */
    @PostMapping("/knowledge-point/{knowledgePointId}")
    public Result<Void> associateQuestionsWithKnowledgePoint(@PathVariable UUID knowledgePointId, @RequestBody List<UUID> questionIds) {
        boolean success = questionKnowledgePointsService.batchCreateAssociationsByKnowledgePointId(knowledgePointId, questionIds);
        return success ? Result.success() : Result.error("关联题目失败");
    }

    /**
     * 创建单个关联关系
     * POST /api/question-knowledge-points
     * Request body: {"questionId": "uuid", "knowledgePointId": "uuid"}
     */
    @PostMapping
    public Result<Void> createAssociation(@RequestBody Map<String, UUID> request) {
        UUID questionId = request.get("questionId");
        UUID knowledgePointId = request.get("knowledgePointId");
        
        if (questionId == null || knowledgePointId == null) {
            return Result.error("题目ID和知识点ID不能为空");
        }
        
        boolean success = questionKnowledgePointsService.createAssociation(questionId, knowledgePointId);
        return success ? Result.success() : Result.error("创建关联失败");
    }
    
    /**
     * 批量创建题目与知识点的关联关系
     * POST /api/question-knowledge-points/batch
     * Request body: [{"knowledgePointId": "uuid", "questionId": "uuid"}, ...]
     */
    @PostMapping("/batch")
    public Result<Void> batchCreateAssociations(@RequestBody List<Map<String, String>> relationsList) {
        if (relationsList == null || relationsList.isEmpty()) {
            return Result.error("关系列表不能为空");
        }
        
        boolean success = true;
        for (Map<String, String> relation : relationsList) {
            String knowledgePointIdStr = relation.get("knowledgePointId");
            String questionIdStr = relation.get("questionId");
            
            if (knowledgePointIdStr == null || questionIdStr == null) {
                return Result.error("知识点ID和题目ID不能为空");
            }
            
            try {
                UUID knowledgePointId = UUID.fromString(knowledgePointIdStr);
                UUID questionId = UUID.fromString(questionIdStr);
                
                boolean result = questionKnowledgePointsService.createAssociation(questionId, knowledgePointId);
                if (!result) {
                    success = false;
                }
            } catch (IllegalArgumentException e) {
                return Result.error("无效的UUID格式");
            }
        }
        
        return success ? Result.success() : Result.error("部分关联关系创建失败");
    }

    /**
     * 删除单个关联关系
     * DELETE /api/question-knowledge-points
     * Request body: {"questionId": "uuid", "knowledgePointId": "uuid"}
     */
    @DeleteMapping
    public Result<Void> deleteAssociation(@RequestBody Map<String, UUID> request) {
        UUID questionId = request.get("questionId");
        UUID knowledgePointId = request.get("knowledgePointId");
        
        if (questionId == null || knowledgePointId == null) {
            return Result.error("题目ID和知识点ID不能为空");
        }
        
        boolean success = questionKnowledgePointsService.deleteAssociation(questionId, knowledgePointId);
        return success ? Result.success() : Result.error("删除关联失败");
    }

    /**
     * 删除题目的所有关联
     * DELETE /api/question-knowledge-points/question/{questionId}
     */
    @DeleteMapping("/question/{questionId}")
    public Result<Void> deleteAssociationsByQuestionId(@PathVariable UUID questionId) {
        boolean success = questionKnowledgePointsService.deleteAssociationsByQuestionId(questionId);
        return success ? Result.success() : Result.error("删除题目关联失败");
    }

    /**
     * 删除知识点的所有关联
     * DELETE /api/question-knowledge-points/knowledge-point/{knowledgePointId}
     */
    @DeleteMapping("/knowledge-point/{knowledgePointId}")
    public Result<Void> deleteAssociationsByKnowledgePointId(@PathVariable UUID knowledgePointId) {
        boolean success = questionKnowledgePointsService.deleteAssociationsByKnowledgePointId(knowledgePointId);
        return success ? Result.success() : Result.error("删除知识点关联失败");
    }

    @GetMapping("/knowledge-point")
    public Result<List<MathKnowledgePoint>> listKnowledgePointByQuestionId(@RequestParam("questionId") UUID questionId, @RequestParam(value = "publisher", required = false) PublisherType publisher) {
        return Result.success(questionKnowledgePointsService.listKnowledgePointByQuestionId(questionId, null, null, publisher));

    }
}
