package com.joinus.knowledge.controller;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.joinus.knowledge.common.Result;
import com.joinus.knowledge.enums.ExamFileType;
import com.joinus.knowledge.enums.OssEnum;
import com.joinus.knowledge.model.dto.pdf.PdfParam;
import com.joinus.knowledge.model.entity.File;
import com.joinus.knowledge.model.entity.MathQuestion;
import com.joinus.knowledge.model.entity.QuestionFile;
import com.joinus.knowledge.model.param.AnalyzeExamParam;
import com.joinus.knowledge.model.param.PageQuestionParam;
import com.joinus.knowledge.model.param.SolveQuestionParam;
import com.joinus.knowledge.model.vo.*;
import com.joinus.knowledge.service.*;
import com.joinus.knowledge.service.impl.OnlyOnceServiceImpl;
import com.joinus.knowledge.service.impl.PdfGenerator;
import com.joinus.knowledge.utils.AliOssUtils;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/basic/api")
public class BasicApiController {

    @Autowired
    private BasicApiService basicApiService;

    @GetMapping("/schools")
    public Result<List<SchoolVO>> listSchools(@RequestParam(value = "schoolName", required = false) String schoolName) {
        return Result.success(basicApiService.listSchools(schoolName));
    }

}
