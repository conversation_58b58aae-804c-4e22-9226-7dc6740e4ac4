package com.joinus.knowledge.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.joinus.knowledge.common.Result;
import com.joinus.knowledge.enums.PublisherType;
import com.joinus.knowledge.enums.QuestionSourceType;
import com.joinus.knowledge.enums.QuestionType;
import com.joinus.knowledge.mapper.TextbooksMapper;
import com.joinus.knowledge.model.entity.Textbooks;
import com.joinus.knowledge.model.param.CreateKeypointParam;
import com.joinus.knowledge.model.param.DeleteKeypointParam;
import com.joinus.knowledge.model.param.QueryKeyPointDetailParam;
import com.joinus.knowledge.model.param.UpdateQuestionDetailParam;
import com.joinus.knowledge.model.vo.*;
import com.joinus.knowledge.service.*;
import jakarta.annotation.Resource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.*;

@RestController
@RequestMapping("/proofread")
public class ProofreadController {

    @Resource
    private TextbooksService textbooksService;
    @Resource
    private TextbooksMapper textbooksMapper;

    @Resource
    private FilesService filesService;

    @Resource
    private MathQuestionsService questionsService;
    @Autowired
    private MathKnowledgePointsService mathKnowledgePointsService;
    @Autowired
    private MathQuestionTypesService mathQuestionTypesService;


    @GetMapping("/textbooks")
    public Result<List<Textbooks>> getAllTextbooks(
            @RequestParam(name = "page", defaultValue = "1") Integer page,
            @RequestParam(name = "size", defaultValue = "10") Integer size,
            @RequestParam(name = "name", required = false) String name,
            @RequestParam(name = "publishers", required = false) List<String> publishers,
            @RequestParam(name = "subject", required = false) String subject,
            @RequestParam(name = "grade", required = false) Integer grade,
            @RequestParam(name = "semester", required = false) Integer semster) {


        LambdaQueryWrapper<Textbooks> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.isNull(Textbooks::getDeletedAt); // 只查询未删除的记录

        if (name != null && !name.isEmpty()) {
            queryWrapper.like(Textbooks::getName, name);
        }

        if (CollUtil.isNotEmpty(publishers)) {
            queryWrapper.in(Textbooks::getPublisher, publishers);
        }

        if (subject != null && !subject.isEmpty()) {
            queryWrapper.like(Textbooks::getSubject, subject);
        }

        if (grade != null) {
            queryWrapper.eq(Textbooks::getGrade, grade);
        }

        if (semster != null) {
            queryWrapper.eq(Textbooks::getSemester, semster);
        }

        // 按更新时间降序排序
        queryWrapper.orderByDesc(Textbooks::getUpdatedAt);

        List<Textbooks> textbooks = textbooksMapper.selectList(queryWrapper);
        textbooks.sort(Comparator.comparing(Textbooks::getPublisher, Comparator.nullsLast(Comparator.naturalOrder()))
                .thenComparing(Textbooks::getGrade, Comparator.nullsLast(Comparator.naturalOrder()))
                .thenComparing(Textbooks::getSemester, Comparator.nullsLast(Comparator.naturalOrder())));
        return Result.success(textbooks);
    }

    @GetMapping("/textbooks/{id}")
    public Result<List<TextbookFileVO>> getTextbookById(@PathVariable("id") UUID id) {
        List<TextbookFileVO> files = filesService.getFilesByBookId(id);
        return Result.success(files);
    }

    @GetMapping("/textbooks/{id}/keypoints/{pageNo}")
    public Result<List<TextbookPointVO>> getKeypoints(@PathVariable("id") String id, @PathVariable("pageNo") Integer pageNo) {
        List<TextbookPointVO> files = textbooksService.getKeypointsByBookIdAndPageNo(id, pageNo);
        return Result.success(files);
    }

    @GetMapping("/keypoints")
    public Result<List<QuestionDetailVO>> getKeypointDetail(QueryKeyPointDetailParam param) {
        List<QuestionDetailVO> results = questionsService.getKeypointDetail(param);
        return Result.success(results);
    }

    @PutMapping("/keypoints")
    public Result<List<QuestionDetailVO>> updateKeypointDetail(@RequestBody UpdateQuestionDetailParam param) {
        List<QuestionDetailVO> results = questionsService.updateKeypointDetail(param);
        return Result.success(results);
    }

    @DeleteMapping("/keypoints")
    public Result<List<QuestionDetailVO>> deleteKeyPoints(@RequestBody DeleteKeypointParam param) {
         questionsService.deleteKeyPoints(param);
        return Result.success();
    }

    @PostMapping("/keypoints/by-pageNo")
    public Result<TextbookPointVO> createKeypointByPageNo(@RequestBody CreateKeypointParam param) {
        TextbookPointVO textbookPointVO = questionsService.createKeypointByPageNo(param);
        return Result.success(textbookPointVO);
    }

    @GetMapping("/keypoints/question-type/math")
    public Result listQuestionType() {
        return Result.success(QuestionType.list());
    }


    @GetMapping("/knowledge-points")
    public Result<List<MathKnowledgePointVO>> listAllKnowledgePoints(@RequestParam(value = "name", required = false) String name,
                                                                     @RequestParam(value = "grade", required = false) Integer grade,
                                                                     @RequestParam(value = "semester", required = false) Integer semester,
                                                                     @RequestParam(value = "publisher", required = false) PublisherType publisher,
                                                                     @RequestParam(value = "chapterId", required = false) UUID chapterId,
                                                                     @RequestParam(value = "chapterName", required = false) String chapterName,
                                                                     @RequestParam(value = "sectionId", required = false) UUID sectionId,
                                                                     @RequestParam(value = "sectionName", required = false) String sectionName) {
        List<MathKnowledgePointVO> list = mathKnowledgePointsService.list(name, grade, semester, publisher, chapterId, chapterName, sectionId, sectionName);
        return Result.success(list);
    }

    @GetMapping("/question-types")
    public Result<List<MathQuestionTypeVO>> listAllQuestionTypes(@RequestParam(value = "name", required = false) String name,
                                                                 @RequestParam(value = "grade", required = false) Integer grade,
                                                                 @RequestParam(value = "semester", required = false) Integer semester,
                                                                 @RequestParam(value = "publisher", required = false) PublisherType publisher,
                                                                 @RequestParam(value = "chapterId", required = false) UUID chapterId,
                                                                 @RequestParam(value = "chapterName", required = false) String chapterName,
                                                                 @RequestParam(value = "sectionId", required = false) UUID sectionId,
                                                                 @RequestParam(value = "sectionName", required = false) String sectionName) {
        List<MathQuestionTypeVO> list = mathQuestionTypesService.list(name, grade, semester, publisher, chapterId, chapterName, sectionId, sectionName);
        return Result.success(list);
    }

    @GetMapping("/questions/source")
    public Result<List<QuestionSourceType>> listQuestionSourceType() {
        return Result.success(Arrays.asList(QuestionSourceType.values()));
    }

    @GetMapping("/publishers")
    public Result<List<Map<Object, Object>>> listPublishers() {
        List<Map<Object, Object>> list = Arrays.stream(PublisherType.values()).map(publisher -> {
            return MapUtil.builder().put("key", publisher.name())
                    .put("value", publisher.getValue())
                    .build();
        }).toList();
        return Result.success(list);
    }

    @GetMapping("/question-type-enums")
    public Result<List<Map<Object, Object>>> listQuestionTypes() {
        List<Map<Object, Object>> list = Arrays.stream(QuestionType.values()).map(publisher -> {
            return MapUtil.builder().put("key", publisher.name())
                    .put("value", publisher.getType())
                    .build();
        }).toList();
        return Result.success(list);
    }

    @GetMapping("/question-sources")
    public Result<List<Map<Object, Object>>> listQuestionSource() {
        List<Map<Object, Object>> list = Arrays.stream(QuestionSourceType.values()).map(publisher -> {
            return MapUtil.builder().put("key", publisher.name())
                    .put("value", publisher.getDescription())
                    .build();
        }).toList();
        return Result.success(list);
    }


}
