package com.joinus.knowledge.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.joinus.knowledge.common.Result;
import com.joinus.knowledge.model.entity.*;
import com.joinus.knowledge.model.vo.EnterBookSaveDerivativeParam;
import com.joinus.knowledge.model.vo.EnterBookSaveFileParam;
import com.joinus.knowledge.model.vo.SectionKeypointVO;
import com.joinus.knowledge.model.vo.SectionVO;
import com.joinus.knowledge.service.*;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * 教材管理 Controller
 */
@RestController
@RequestMapping("/textbooks")
@RequiredArgsConstructor
public class TextbooksController {

    private final TextbooksService textbooksService;

    private final FilesService filesService;

    private final TextbookFileService textbookFileService;

    private final FileDerivativeService fileDerivativeService;

    /**
     * 分页查询教材列表
     * GET /api/textbooks
     */
    @GetMapping
    public Result<IPage<Textbooks>> list(
            @RequestParam(name = "page", defaultValue = "1") Integer page,
            @RequestParam(name = "size", defaultValue = "10") Integer size,
            @RequestParam(name = "name", required = false) String name,
            @RequestParam(name = "publisher", required = false) String publisher,
            @RequestParam(name = "subject", required = false) String subject,
            @RequestParam(name = "grade", required = false) Integer grade,
            @RequestParam(name = "semester", required = false) Integer semster) {
        
        Page<Textbooks> pageParam = new Page<>(page, size);
        
        LambdaQueryWrapper<Textbooks> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.isNull(Textbooks::getDeletedAt); // 只查询未删除的记录
        
        if (name != null && !name.isEmpty()) {
            queryWrapper.like(Textbooks::getName, name);
        }
        
        if (publisher != null && !publisher.isEmpty()) {
            queryWrapper.like(Textbooks::getPublisher, publisher);
        }
        
        if (subject != null && !subject.isEmpty()) {
            queryWrapper.like(Textbooks::getSubject, subject);
        }

        if (grade != null) {
            queryWrapper.eq(Textbooks::getGrade, grade);
        }

        if (semster != null) {
            queryWrapper.eq(Textbooks::getSemester, semster);
        }
        
        // 按更新时间降序排序
        queryWrapper.orderByDesc(Textbooks::getUpdatedAt);
        
        IPage<Textbooks> result = textbooksService.page(pageParam, queryWrapper);
        return Result.success(result);
    }

    /**
     * 获取所有教材列表（不分页）
     * GET /api/textbooks/all
     */
    @GetMapping("/all")
    public Result<List<Textbooks>> listAll() {
        LambdaQueryWrapper<Textbooks> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.isNull(Textbooks::getDeletedAt);
        queryWrapper.orderByDesc(Textbooks::getUpdatedAt);
        List<Textbooks> list = textbooksService.list(queryWrapper);
        return Result.success(list);
    }

    /**
     * 根据ID获取教材详情
     * GET /api/textbooks/{id}
     */
    @GetMapping("/{id}")
    public Result<Textbooks> getById(@PathVariable(name = "id") UUID id) {
        Textbooks textbook = textbooksService.getById(id);
        if (textbook == null || textbook.getDeletedAt() != null) {
            return Result.error("教材不存在或已删除");
        }
        return Result.success(textbook);
    }

    /**
     * 创建新教材
     * POST /api/textbooks
     */
    @PostMapping
    public Result<Textbooks> create(@RequestBody Textbooks textbook) {
        // MyBatis-Plus元对象处理器会自动设置ID、创建时间和更新时间
        LambdaQueryWrapper<Textbooks> eq = Wrappers.lambdaQuery(Textbooks.class)
                .eq(Textbooks::getName, textbook.getName())
                .eq(Textbooks::getPublisher, textbook.getPublisher())
                .eq(Textbooks::getSubject, textbook.getSubject());
        if (textbooksService.getOne(eq) != null) {
            return Result.error("教材已存在");
        }
        boolean success = textbooksService.save(textbook);
        if (!success) {
            return Result.error("创建教材失败");
        }
        return Result.success(textbook);
    }

    /**
     * 更新教材信息
     * PUT /api/textbooks/{id}
     */
    @PutMapping("/{id}")
    public Result<Textbooks> update(@PathVariable(name = "id") UUID id, @RequestBody Textbooks textbook) {
        Textbooks existingTextbook = textbooksService.getById(id);
        if (existingTextbook == null || existingTextbook.getDeletedAt() != null) {
            return Result.error("教材不存在或已删除");
        }
        
        // 设置ID
        textbook.setId(id);
        // MyBatis-Plus会自动更新updatedAt字段
        
        boolean success = textbooksService.updateById(textbook);
        if (!success) {
            return Result.error("更新教材失败");
        }
        return Result.success(textbook);
    }

    /**
     * 删除教材（逻辑删除）
     * DELETE /api/textbooks/{id}
     */
    @DeleteMapping("/{id}")
    public Result<Void> delete(@PathVariable(name = "id") UUID id) {
        boolean success = textbooksService.logicalDelete(id);
        if (!success) {
            return Result.error("删除教材失败");
        }
        return Result.success();
    }

    /**
     * 物理删除教材
     * DELETE /api/textbooks/{id}/permanent
     */
    @DeleteMapping("/{id}/permanent")
    public Result<Void> permanentDelete(@PathVariable(name = "id") UUID id) {
        boolean success = textbooksService.removeById(id);
        if (!success) {
            return Result.error("物理删除教材失败");
        }
        return Result.success();
    }

    /**
     * 恢复已删除的教材
     * PUT /api/textbooks/{id}/restore
     */
    @PutMapping("/{id}/restore")
    public Result<Void> restore(@PathVariable(name = "id") UUID id) {
        boolean success = textbooksService.restore(id);
        if (!success) {
            return Result.error("恢复教材失败");
        }
        return Result.success();
    }

    /**
     * 根据学科获取教材列表
     * GET /api/textbooks/subject/{subject}
     */
    @GetMapping("/subject/{subject}")
    public Result<List<Textbooks>> getBySubject(@PathVariable(name = "subject") String subject) {
        LambdaQueryWrapper<Textbooks> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Textbooks::getSubject, subject);
        queryWrapper.isNull(Textbooks::getDeletedAt);
        queryWrapper.orderByDesc(Textbooks::getUpdatedAt);
        
        List<Textbooks> list = textbooksService.list(queryWrapper);
        return Result.success(list);
    }
    
    /**
     * 导入教材图片文件
     * POST /api/textbooks/{id}/import-images
     *
     * @param id 教材ID
     * @param folderPath 文件夹路径，结构为：
     *                  -- bookName
     *                    -- pageNo
     *                      -- origin.png
     * @return 导入结果
     */
    @PostMapping("/{id}/import-images")
    public Result<String> importImages(
            @PathVariable(name = "id") UUID id,
            @RequestParam(name = "folderPath") String folderPath) {
        

        try {
            int count = textbooksService.importImagesAndLinkToTextbook(id, folderPath);
            return Result.success("成功导入 " + count + " 个教材页面图片");
        } catch (Exception e) {
            return Result.error("导入图片失败: " + e.getMessage());
        }
    }
    
    /**
     * 根据教材ID查询所有页面信息，包括页码和图片地址
     * 
     * @param id 教材ID
     * @return 页面信息列表
     */
    @GetMapping("/{id}/pages")
    public Result<List<Map<String, Object>>> getTextbookPages(@PathVariable(name = "id") UUID id) {
        try {
            List<Map<String, Object>> pages = textbooksService.getTextbookPages(id);
            return Result.success(pages);
        } catch (Exception e) {
            return Result.error("查询教材页面信息失败: " + e.getMessage());
        }
    }

    /**
     * 检查教材某一页面是否存在
     *
     * @param textbookId 教材ID
     * @param pageNo 页码
     * @return 页面是否存在
     */
    @GetMapping("/entering-books/{id}/check")
    public Result<Boolean> checkExistPage(@PathVariable("id") UUID textbookId,
                                          @RequestParam("pageNo") int pageNo) {
        try {
            boolean exist = textbooksService.checkExistPage(textbookId, pageNo);
            return Result.success(exist);
        } catch (Exception e) {
            return Result.error("查询教材页面信息失败: " + e.getMessage());
        }
    }

    /**
     * 保存书本文件
     * @param textbookId
     * @param param
     * @return
     */
    @PostMapping("/entering-books/{id}/file")
    public Result<UUID> saveFile(@PathVariable("id") UUID textbookId,
                                          @RequestBody EnterBookSaveFileParam param) {
        try {
            File fileEntity = filesService.save("page_" + param.getPageNo() + "." + param.getOriginalImageType(), param.getOriginalImageType(), param.getOriginalMimeType(), param.getOssKey(), param.getOssEnum());
            textbookFileService.saveRelation(textbookId, fileEntity.getId(), param.getPageNo());
            return Result.success(fileEntity.getId());
        } catch (Exception e) {
            return Result.error("上传文件失败: " + e.getMessage());
        }
    }

    @PostMapping("/entering-books/file-derivative")
    public Result<Boolean> saveFileDerivative(@RequestBody EnterBookSaveDerivativeParam param) {
        try {
            // 保存衍生文件记录
            FileDerivative derivative = new FileDerivative();
            derivative.setId(UUID.randomUUID());
            derivative.setFileId(param.getFileId());
            derivative.setDerivativeType(param.getDerivativeType());
            derivative.setStoragePath(param.getStoragePath());
            derivative.setFormat(param.getFormat());
            derivative.setWidth(param.getWidth());
            derivative.setHeight(param.getHeight());
            derivative.setFileSize(param.getFileSize());

            fileDerivativeService.save(derivative);
            return Result.success(true);
        } catch (Exception e) {
            return Result.error("上传文件失败: " + e.getMessage());
        }
    }


    @GetMapping("/{id}/knowledge-points-and-question-types")
    public Result<List<SectionKeypointVO>> listAllKnowledgePointsAndQuestionTypes(@PathVariable("id") UUID id) {
        List<SectionKeypointVO> list = textbooksService.listAllKnowledgePointsAndQuestionTypes(id);
        return Result.success(list);
    }


    @GetMapping("/{id}/chapters")
    public Result<List<MathChapter>> listAllChapters(@PathVariable("id") UUID id) {
        List<MathChapter> list = textbooksService.listAllChapters(id);
        return Result.success(list);
    }

    @GetMapping("/{id}/sections")
    public Result<List<SectionVO>> listAllSections(@PathVariable("id") UUID id) {
        List<SectionVO> list = textbooksService.listAllSections(id);
        return Result.success(list);
    }
}
