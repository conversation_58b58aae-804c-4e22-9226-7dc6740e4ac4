package com.joinus.knowledge.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import com.joinus.knowledge.common.Result;
import com.joinus.knowledge.enums.MathGraphicsScriptStatusEnum;
import com.joinus.knowledge.model.entity.*;
import com.joinus.knowledge.model.param.GraphicsSubmitParam;
import com.joinus.knowledge.model.po.SimpleUser;
import com.joinus.knowledge.model.vo.*;
import com.joinus.knowledge.service.*;
import com.joinus.knowledge.utils.AliOssUtils;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.Collectors;

@Tag(name = "数学几何题补图相关接口", description = "提供数学几何题补图相关功能")
@RestController
@RequestMapping("/math/question/graphics")
@RequiredArgsConstructor
@Slf4j
public class MathQuestionGraphicsController {

    @Resource
    private QuestionGraphicsScriptService questionGraphicsScriptService;
    @Resource
    private MathQuestionsService mathQuestionsService;
    @Resource
    private MathLabelService mathLabelService;
    @Resource
    private QuestionLabelService questionLabelService;
    @Resource
    private FilesService filesService;
    @Resource
    private MathAnswersService mathAnswersService;
    @Resource
    private MathQuestionRelationshipsService mathQuestionRelationshipsService;
    @ApolloJsonValue("${question.script.admins:[{name:李青亚,password:0bb1a3590e2b0b3a39f5b70c5241547b},{name:admin,password:d1d0d512a959cbc9e7f491135c82226b}]}")
    private List<SimpleUser> adminList;
    private static final String PASSWORD_SALT = "butu";

    @PostMapping("/claim")
    public Result<Integer> claimScripts(@RequestParam("count") Integer count, @RequestParam("username") String username) {
        Integer claimedCount = questionGraphicsScriptService.claimScripts(count, username);
        return Result.success(claimedCount);
    }

    @GetMapping("/page-by-user")
    public Result<Page<MathQuestionGraphicsScriptVO>> page(@RequestParam(name = "page", defaultValue = "1") Integer page,
                                                                              @RequestParam(name = "size", defaultValue = "10") Integer size,
                                                                              @RequestParam(name = "username", required = false) String username,
                                                                              @RequestParam(name = "labelName", required = false) String labelName,
                                                                              @RequestParam(name = "questionId", required = false) UUID questionId,
                                                                              @RequestParam(name = "status", required = false) String status,
                                                                              @RequestParam(name = "startDateStr", required = false) String startDateStr,
                                                                              @RequestParam(name = "endDateStr", required = false) String endDateStr) {
        Page<MathQuestionGraphicsScriptVO> pageParam = new Page<>(page, size);

        Date startDate = null;
        Date endDate = null;
        if (StrUtil.isNotBlank(startDateStr)) {
            startDate = DateUtil.parseDate(startDateStr);
        }
        if (StrUtil.isNotBlank(endDateStr)) {
            endDate = DateUtil.offsetDay(DateUtil.parseDate(endDateStr), 1);
        }

        Page<MathQuestionGraphicsScriptVO> resultPage = mathQuestionsService.queryQuestionGraphicsScriptByUser(pageParam, username, labelName, questionId, status, startDate, endDate);

        if (CollUtil.isNotEmpty(resultPage.getRecords())) {
            List<UUID> questionIds = resultPage.getRecords().stream().map(MathQuestionVO::getId).toList();

            List<MathLabel> labelList = mathLabelService.lambdaQuery().list();

            Map<UUID, MathLabel> labelMap = labelList.stream().collect(Collectors.toMap(MathLabel::getId, label -> label));

            Map<UUID, List<MathLabel>> questionLabelMap = questionLabelService.lambdaQuery()
                    .in(QuestionLabel::getQuestionId, questionIds)
                    .list()
                    .stream()
                    .collect(Collectors.groupingBy(QuestionLabel::getQuestionId, Collectors.mapping(QuestionLabel::getLabelId, Collectors.mapping(labelMap::get, Collectors.toList()))));

            Map<UUID, UUID> baseQuestionMap = mathQuestionRelationshipsService.lambdaQuery()
                    .in(MathQuestionRelationships::getDerivedQuestionId, questionIds)
                    .list()
                    .stream()
                    .collect(Collectors.toMap(MathQuestionRelationships::getDerivedQuestionId, MathQuestionRelationships::getBaseQuestionId));

            resultPage.getRecords().forEach(question -> {
                question.setLabels(questionLabelMap.getOrDefault(question.getId(), List.of())
                        .stream()
                        .map(MathLabelVO::ofLabel)
                        .toList());
                question.setBaseQuestionId(baseQuestionMap.get(question.getId()));
            });
        }

        return Result.success(resultPage);

    }

    @GetMapping("/detail")
    public Result<QuestionGraphicsScriptDetailVO> detail(@RequestParam("questionId") UUID questionId) {
        List<QuestionGraphicsScript> list = questionGraphicsScriptService.lambdaQuery().eq(QuestionGraphicsScript::getQuestionId, questionId).list();
        if (CollUtil.isEmpty(list)) {
            return Result.success();
        }
        QuestionGraphicsScript questionGraphicsScript = list.getFirst();
        QuestionGraphicsScriptDetailVO detailVO = BeanUtil.copyProperties(questionGraphicsScript, QuestionGraphicsScriptDetailVO.class);
        if (questionGraphicsScript.getFileId() != null) {
            File file = filesService.getById(detailVO.getFileId());
            detailVO.setUrl(AliOssUtils.generatePresignedUrlStatic(file.getOssBucket(), file.getOssUrl()));
        }
        return Result.success(detailVO);
    }

    @PostMapping("/script/save")
    public Result<String> saveScript(@RequestBody QuestionGraphicsScript script) {
        questionGraphicsScriptService.lambdaUpdate()
                .set(QuestionGraphicsScript::getHumanScript, script.getHumanScript())
                .set(QuestionGraphicsScript::getUpdatedAt, new Date())
                .eq(QuestionGraphicsScript::getQuestionId, script.getQuestionId())
                .update();
        return Result.success("保存成功");
    }

    @GetMapping("/admins")
    public Result<List<String>> getAdmins() {
        return Result.success(adminList.stream().map(SimpleUser::getName).toList());
    }

    @GetMapping("/users")
    public Result<Set<String>> getUsers() {
        Set<String> users = questionGraphicsScriptService.lambdaQuery()
                .isNotNull(QuestionGraphicsScript::getUsername)
                .list()
                .stream()
                .map(QuestionGraphicsScript::getUsername)
                .collect(Collectors.toSet());
        return Result.success(users);
    }

    @PostMapping("/admin/validate")
    public Result<Boolean> validateAdmin(@RequestBody SimpleUser user) {
        boolean result = adminList.stream().anyMatch(admin -> admin.getName().equals(user.getName()) && admin.getPassword().equals(SecureUtil.md5(user.getPassword() + PASSWORD_SALT)));
        return Result.success(result);
    }

    @PostMapping("/submit")
    public Result<String> submit(@RequestBody GraphicsSubmitParam graphicsSubmitParam) {
        questionGraphicsScriptService.submit(graphicsSubmitParam);
        return Result.success("提交成功");
    }

    @PostMapping("/verify")
    public Result<String> verify(@RequestParam("questionId") UUID questionId, @RequestParam("verified") boolean verified, @RequestParam("remark") String remark) {
        questionGraphicsScriptService.verify(questionId, verified, remark);
        return Result.success("操作成功");
    }

    @PostMapping("/ignore")
    public Result<String> ignore(@RequestBody GraphicsSubmitParam graphicsSubmitParam) {
        questionGraphicsScriptService.ignore(graphicsSubmitParam.questionId(), graphicsSubmitParam.remark());
        return Result.success("提交成功");
    }

}
