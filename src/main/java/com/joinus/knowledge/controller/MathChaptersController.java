package com.joinus.knowledge.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.joinus.knowledge.common.Result;
import com.joinus.knowledge.enums.PublisherType;
import com.joinus.knowledge.model.entity.MathChapter;
import com.joinus.knowledge.model.vo.MathChapterVO;
import com.joinus.knowledge.service.MathChapterService;
import lombok.RequiredArgsConstructor;
import org.apache.ibatis.annotations.Param;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * 数学章节管理 Controller
 */
@RestController
@RequestMapping("/math/chapters")
@RequiredArgsConstructor
public class MathChaptersController {

    private final MathChapterService mathChapterService;

    /**
     * 查询所有章节
     * GET /api/math/chapters
     */
    @GetMapping
    public Result<List<MathChapterVO>> list(@RequestParam(value = "name", required = false) String name,
                                            @RequestParam(value = "grade", required = false) Integer grade,
                                            @RequestParam(value = "semester", required = false) Integer semester,
                                            @RequestParam(value = "publisher", required = false) PublisherType publisher) {
        List<MathChapterVO> list = mathChapterService.list(name, grade, semester, publisher);
        return Result.success(list);
    }

    /**
     * 分页查询章节
     * GET /api/math/chapters/page?page=1&size=10
     */
    @GetMapping("/page")
    public Result<Page<MathChapter>> page(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) String textbook,
            @RequestParam(required = false) String semester) {
        
        Page<MathChapter> pageParam = new Page<>(page, size);
        LambdaQueryWrapper<MathChapter> queryWrapper = new LambdaQueryWrapper<>();
        
        // 添加查询条件
        if (textbook != null && !textbook.isEmpty()) {
            queryWrapper.like(MathChapter::getTextbook, textbook);
        }
        
        if (semester != null && !semester.isEmpty()) {
            queryWrapper.eq(MathChapter::getSemester, semester);
        }
        
        // 按排序号排序
        queryWrapper.orderByAsc(MathChapter::getSortNo);
        
        Page<MathChapter> resultPage = mathChapterService.page(pageParam, queryWrapper);
        return Result.success(resultPage);
    }

    /**
     * 根据ID查询章节
     * GET /api/math/chapters/{id}
     */
    @GetMapping("/{id}")
    public Result<MathChapter> getById(@PathVariable UUID id) {
        MathChapter mathChapter = mathChapterService.getById(id);
        if (mathChapter == null) {
            return Result.error(404, "章节不存在");
        }
        return Result.success(mathChapter);
    }

    /**
     * 创建章节
     * POST /api/math/chapters
     */
    @PostMapping
    public Result<MathChapter> create(@RequestBody MathChapter mathChapter) {
        // 检查是否存在相同的章节（name, textbook, semester都相同）
        LambdaQueryWrapper<MathChapter> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MathChapter::getName, mathChapter.getName())
                   .eq(MathChapter::getTextbook, mathChapter.getTextbook())
                   .eq(MathChapter::getSemester, mathChapter.getSemester());
        
        // 检查是否已存在
        MathChapter existingChapter = mathChapterService.getOne(queryWrapper);
        if (existingChapter != null) {
            // 如果已存在相同章节，返回已存在的章节
            return Result.success(existingChapter);
        }
        
        // ID will be automatically generated by MyBatis-Plus
        boolean success = mathChapterService.save(mathChapter);
        if (success) {
            return Result.success(mathChapter);
        }
        return Result.error("创建章节失败");
    }
    
    /**
     * 批量创建章节
     * POST /api/math/chapters/batch
     */
    @PostMapping("/batch")
    public Result<Boolean> batchCreate(@RequestBody List<MathChapter> mathChapterList) {
        if (mathChapterList == null || mathChapterList.isEmpty()) {
            return Result.error("章节列表不能为空");
        }
        
        // 过滤掉重复的章节
        List<MathChapter> chaptersToSave = new ArrayList<>();
        
        for (MathChapter chapter : mathChapterList) {
            // 检查是否存在相同的章节（name, textbook, semester都相同）
            LambdaQueryWrapper<MathChapter> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(MathChapter::getName, chapter.getName())
                       .eq(MathChapter::getTextbook, chapter.getTextbook())
                       .eq(MathChapter::getSemester, chapter.getSemester());
            
            // 检查是否已存在
            MathChapter existingChapter = mathChapterService.getOne(queryWrapper);
            if (existingChapter == null) {
                // 只有不存在的章节才添加到待保存列表
                chaptersToSave.add(chapter);
            }
        }
        
        if (chaptersToSave.isEmpty()) {
            // 所有章节都已存在，无需添加
            return Result.success(true);
        }
        
        // 为每个章节生成UUID
        chaptersToSave.forEach(MathChapter::generateUUID);
        
        // 批量保存章节
        boolean success = mathChapterService.saveBatch(chaptersToSave);
        
        if (success) {
            return Result.success(true);
        }
        return Result.error("批量创建章节失败");
    }
    
    /**
     * 更新章节
     * PUT /api/math/chapters/{id}
     */
    @PutMapping("/{id}")
    public Result<MathChapter> update(@PathVariable UUID id, @RequestBody MathChapter mathChapter) {
        // 确保要更新的ID正确
        mathChapter.setId(id);
        boolean success = mathChapterService.updateById(mathChapter);
        if (success) {
            return Result.success(mathChapter);
        }
        return Result.error("更新章节失败");
    }

    /**
     * 删除章节
     * DELETE /api/math/chapters/{id}
     */
    @DeleteMapping("/{id}")
    public Result<Boolean> delete(@PathVariable UUID id) {
        boolean success = mathChapterService.removeById(id);
        if (success) {
            return Result.success(true);
        }
        return Result.error("删除章节失败");
    }


}
