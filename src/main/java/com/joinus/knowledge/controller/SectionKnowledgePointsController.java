package com.joinus.knowledge.controller;

import com.joinus.knowledge.common.Result;
import com.joinus.knowledge.model.entity.MathKnowledgePoint;
import com.joinus.knowledge.model.entity.MathSection;
import com.joinus.knowledge.model.vo.MathKnowledgePointVO;
import com.joinus.knowledge.service.MathKnowledgePointsService;
import com.joinus.knowledge.service.MathSectionService;
import com.joinus.knowledge.service.SectionKnowledgePointsService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * 小节-知识点关联关系管理 Controller
 */
@RestController
@RequestMapping("/api/section-knowledge-points")
@RequiredArgsConstructor
public class SectionKnowledgePointsController {

    private final SectionKnowledgePointsService sectionKnowledgePointsService;
    private final MathSectionService mathSectionService;
    private final MathKnowledgePointsService mathKnowledgePointsService;

    /**
     * 根据小节ID获取关联的所有知识点
     * GET /api/section-knowledge-points/section/{sectionId}
     */
    @GetMapping("/section/{sectionId}")
    public Result<List<MathKnowledgePointVO>> getKnowledgePointsBySectionId(@PathVariable UUID sectionId) {
        // 获取知识点ID列表
        List<UUID> knowledgePointIds = sectionKnowledgePointsService.getKnowledgePointIdsBySectionId(sectionId);
        
        if (knowledgePointIds.isEmpty()) {
            return Result.success(List.of());
        }
        
        // 查询知识点信息
        List<MathKnowledgePointVO> knowledgePoints = mathKnowledgePointsService.listByIds(knowledgePointIds);
        return Result.success(knowledgePoints);
    }

    /**
     * 根据知识点ID获取关联的所有小节
     * GET /api/section-knowledge-points/knowledge-point/{KnowledgePointId}
     */
    @GetMapping("/knowledge-point/{knowledgePointId}")
    public Result<List<MathSection>> getSectionsByKnowledgePointId(@PathVariable UUID knowledgePointId) {
        // 获取小节ID列表
        List<UUID> sectionIds = sectionKnowledgePointsService.getSectionIdsByKnowledgePointId(knowledgePointId);
        
        if (sectionIds.isEmpty()) {
            return Result.success(List.of());
        }
        
        // 查询小节信息
        List<MathSection> sections = mathSectionService.listByIds(sectionIds);
        return Result.success(sections);
    }

    /**
     * 批量关联知识点到一个小节
     * POST /api/section-knowledge-points/section/{sectionId}
     * Request body: ["KnowledgePointId1", "KnowledgePointId2", ...]
     */
    @PostMapping("/section/{sectionId}")
    public Result<Void> associateKnowledgePointsWithSection(@PathVariable UUID sectionId, @RequestBody List<UUID> knowledgePointIds) {
        boolean success = sectionKnowledgePointsService.batchCreateAssociationsBySectionId(sectionId, knowledgePointIds);
        return success ? Result.success() : Result.error("关联知识点失败");
    }

    /**
     * 批量关联小节到一个知识点
     * POST /api/section-knowledge-points/knowledge-point/{KnowledgePointId}
     * Request body: ["sectionId1", "sectionId2", ...]
     */
    @PostMapping("/knowledge-point/{knowledgePointId}")
    public Result<Void> associateSectionsWithKnowledgePoint(@PathVariable UUID knowledgePointId, @RequestBody List<UUID> sectionIds) {
        boolean success = sectionKnowledgePointsService.batchCreateAssociationsByKnowledgePointId(knowledgePointId, sectionIds);
        return success ? Result.success() : Result.error("关联小节失败");
    }

    /**
     * 创建单个关联关系
     * POST /api/section-knowledge-points
     * Request body: {"sectionId": "uuid", "KnowledgePointId": "uuid"}
     */
    @PostMapping
    public Result<Void> createAssociation(@RequestBody Map<String, UUID> request) {
        UUID sectionId = request.get("sectionId");
        UUID knowledgePointId = request.get("knowledgePointId");
        
        if (sectionId == null || knowledgePointId == null) {
            return Result.error("小节ID和知识点ID不能为空");
        }
        
        boolean success = sectionKnowledgePointsService.createAssociation(sectionId, knowledgePointId);
        return success ? Result.success() : Result.error("创建关联失败");
    }

    /**
     * 删除单个关联关系
     * DELETE /api/section-knowledge-points
     * Request body: {"sectionId": "uuid", "knowledgePointId": "uuid"}
     */
    @DeleteMapping
    public Result<Void> deleteAssociation(@RequestBody Map<String, UUID> request) {
        UUID sectionId = request.get("sectionId");
        UUID knowledgePointId = request.get("knowledgePointId");
        
        if (sectionId == null || knowledgePointId == null) {
            return Result.error("小节ID和知识点ID不能为空");
        }
        
        boolean success = sectionKnowledgePointsService.deleteAssociation(sectionId, knowledgePointId);
        return success ? Result.success() : Result.error("删除关联失败");
    }

    /**
     * 删除小节的所有关联
     * DELETE /api/section-knowledge-points/section/{sectionId}
     */
    @DeleteMapping("/section/{sectionId}")
    public Result<Void> deleteAssociationsBySectionId(@PathVariable UUID sectionId) {
        boolean success = sectionKnowledgePointsService.deleteAssociationsBySectionId(sectionId);
        return success ? Result.success() : Result.error("删除小节关联失败");
    }

    /**
     * 删除知识点的所有关联
     * DELETE /api/section-knowledge-points/knowledge-point/{KnowledgePointId}
     */
    @DeleteMapping("/knowledge-point/{knowledgePointId}")
    public Result<Void> deleteAssociationsByKnowledgePointId(@PathVariable UUID knowledgePointId) {
        boolean success = sectionKnowledgePointsService.deleteAssociationsByKnowledgePointId(knowledgePointId);
        return success ? Result.success() : Result.error("删除知识点关联失败");
    }
}
