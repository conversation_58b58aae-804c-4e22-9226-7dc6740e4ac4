package com.joinus.knowledge.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.joinus.knowledge.common.Result;
import com.joinus.knowledge.model.dto.MathQuestionTypesSlimDTO;
import com.joinus.knowledge.model.entity.MathQuestionType;
import com.joinus.knowledge.service.MathQuestionTypesService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * 数学题型管理 Controller
 */
@RestController
@RequestMapping("/api/math/question-types")
@RequiredArgsConstructor
public class MathQuestionTypesController {

    private final MathQuestionTypesService mathQuestionTypesService;

    /**
     * 查询所有题型
     * GET /api/math/question-types
     */
    @GetMapping
    public Result<List<MathQuestionType>> list() {
        return Result.success(mathQuestionTypesService.list());
    }
    
    /**
     * 查询所有题型（精简版，只返回id、name和sortNo字段）
     * GET /api/math/question-types/slim
     */
    @GetMapping("/slim")
    public Result<List<MathQuestionTypesSlimDTO>> listSlim() {
        return Result.success(mathQuestionTypesService.listSlim());
    }

    /**
     * 分页查询题型
     * GET /api/math/question-types/page?page=1&size=10
     */
    @GetMapping("/page")
    public Result<Page<MathQuestionType>> page(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) String name) {
        
        Page<MathQuestionType> pageParam = new Page<>(page, size);
        LambdaQueryWrapper<MathQuestionType> queryWrapper = new LambdaQueryWrapper<>();
        
        // 添加查询条件
        if (name != null && !name.isEmpty()) {
            queryWrapper.like(MathQuestionType::getName, name);
        }
        
        // 按排序号排序
        queryWrapper.orderByAsc(MathQuestionType::getSortNo);
        
        Page<MathQuestionType> resultPage = mathQuestionTypesService.page(pageParam, queryWrapper);
        return Result.success(resultPage);
    }

    /**
     * 根据ID查询题型
     * GET /api/math/question-types/{id}
     */
    @GetMapping("/{id}")
    public Result<MathQuestionType> getById(@PathVariable UUID id) {
        MathQuestionType questionType = mathQuestionTypesService.getById(id);
        if (questionType == null) {
            return Result.error(404, "题型不存在");
        }
        return Result.success(questionType);
    }

    /**
     * 创建题型
     * POST /api/math/question-types
     */
    @PostMapping
    public Result<MathQuestionType> create(@RequestBody MathQuestionType questionType) {
        // 检查是否存在相同的题型（名称相同）
        LambdaQueryWrapper<MathQuestionType> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MathQuestionType::getName, questionType.getName());
        
        // 检查是否已存在
        MathQuestionType existingType = mathQuestionTypesService.getOne(queryWrapper);
        if (existingType != null) {
            // 如果已存在相同题型，返回错误信息
            return Result.error("题型已存在");
        }
        
        // 生成UUID
        questionType.generateUUID();
        
        boolean success = mathQuestionTypesService.save(questionType);
        if (success) {
            return Result.success(questionType);
        }
        return Result.error("创建题型失败");
    }
    
    /**
     * 批量创建题型
     * POST /api/math/question-types/batch
     */
    @PostMapping("/batch")
    public Result<Boolean> batchCreate(@RequestBody List<MathQuestionType> questionTypesList) {
        if (questionTypesList == null || questionTypesList.isEmpty()) {
            return Result.error("题型列表不能为空");
        }
        
        // 过滤掉重复的题型
        List<MathQuestionType> typesToSave = new ArrayList<>();
        
        for (MathQuestionType type : questionTypesList) {
            // 检查是否存在相同的题型（名称相同）
            LambdaQueryWrapper<MathQuestionType> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(MathQuestionType::getName, type.getName());
            
            // 检查是否已存在
            MathQuestionType existingType = mathQuestionTypesService.getOne(queryWrapper);
            if (existingType == null) {
                // 只有不存在的题型才添加到待保存列表
                typesToSave.add(type);
            }
        }
        
        if (typesToSave.isEmpty()) {
            // 所有题型都已存在，无需添加
            return Result.success(true);
        }
        
        // 为每个题型生成UUID
        typesToSave.forEach(MathQuestionType::generateUUID);
        
        // 批量保存题型
        boolean success = mathQuestionTypesService.saveBatch(typesToSave);
        
        if (success) {
            return Result.success(true);
        }
        return Result.error("批量创建题型失败");
    }
    
    /**
     * 更新题型
     * PUT /api/math/question-types/{id}
     */
    @PutMapping("/{id}")
    public Result<MathQuestionType> update(@PathVariable UUID id, @RequestBody MathQuestionType questionType) {
        // 确保要更新的ID正确
        questionType.setId(id);
        boolean success = mathQuestionTypesService.updateById(questionType);
        if (success) {
            return Result.success(questionType);
        }
        return Result.error("更新题型失败");
    }

    /**
     * 删除题型
     * DELETE /api/math/question-types/{id}
     */
    @DeleteMapping("/{id}")
    public Result<Boolean> delete(@PathVariable UUID id) {
        boolean success = mathQuestionTypesService.removeById(id);
        if (success) {
            return Result.success(true);
        }
        return Result.error("删除题型失败");
    }
}
