package com.joinus.knowledge.controller;

import com.joinus.knowledge.model.dto.MathTrainingHtmlDTO;
import com.joinus.knowledge.model.param.SpecialTrainingParam;
import com.joinus.knowledge.model.vo.MathTrainingHtmlVO;
import com.joinus.knowledge.service.AIAbilityService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.UUID;

/**
 * 数学专项训练 API 控制器
 */
@Tag(name = "数学专项训练 API", description = "提供数学专项训练相关的 API")
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/math-training")
public class MathTrainingApiController {

    @Resource
    private AIAbilityService aiAbilityService;

    /**
     * 获取数学专项训练 HTML
     *
     * @param param 参数
     * @return HTML 内容
     */
    @Operation(summary = "获取数学专项训练 HTML", description = "根据试卷ID获取数学专项训练的 HTML 内容")
    @PostMapping(value = "/html/questions", produces = MediaType.TEXT_HTML_VALUE)
    public String getMathTrainingHtml(@RequestBody SpecialTrainingParam param) {
        try {
            MathTrainingHtmlDTO htmlVO = aiAbilityService.mathSpecialTraining(param);
            return htmlVO.getQuestionHtml();
        } catch (IllegalArgumentException e) {
            log.error("Invalid UUID format: {}", param.getExamId(), e);
            return "<html><body><h1>错误</h1><p>无效的试卷ID格式</p></body></html>";
        } catch (Exception e) {
            log.error("Error getting question HTML for examId: {}", param.getExamId(), e);
            return "<html><body><h1>错误</h1><p>获取题目失败: " + e.getMessage() + "</p></body></html>";
        }
    }

    /**
     * 获取数学专项训练答案 HTML
     *
     * @param param 试卷参数
     * @return 答案HTML内容
     */
    @Operation(summary = "获取数学专项训练答案 HTML", description = "根据试卷ID获取数学专项训练的答案 HTML 内容")
    @PostMapping(value = "/html/question-answers", produces = MediaType.TEXT_HTML_VALUE)
    public String getMathTrainingAnswerHtml(@RequestBody SpecialTrainingParam param) {
        UUID examId = param.getExamId();
        try {
            MathTrainingHtmlDTO htmlVO = aiAbilityService.mathSpecialTraining(param);
            return htmlVO.getQuestionWithAnswerHtml();
        } catch (IllegalArgumentException e) {
            log.error("Invalid UUID format: {}", examId, e);
            return "<html><body><h1>错误</h1><p>无效的试卷ID格式</p></body></html>";
        } catch (Exception e) {
            log.error("Error getting answer HTML for examId: {}", examId, e);
            return "<html><body><h1>错误</h1><p>获取答案失败: " + e.getMessage() + "</p></body></html>";
        }
    }

    /**
     * 获取数学专项训练带分离答案的 HTML
     *
     * @param examId 试卷ID
     * @return 题目和分离答案的HTML内容
     */
    @Operation(summary = "获取数学专项训练带分离答案的 HTML", description = "根据试卷ID获取数学专项训练的题目和分离答案的 HTML 内容")
    @PostMapping(value = "/html/question-answers/separate", produces = MediaType.TEXT_HTML_VALUE)
    public String getMathTrainingSeparateAnswerHtml(@RequestBody SpecialTrainingParam param) {
        UUID examId = param.getExamId();
        try {
            MathTrainingHtmlDTO htmlVO = aiAbilityService.mathSpecialTraining(param);
            return htmlVO.getSeparateAnswersHtml();
        } catch (IllegalArgumentException e) {
            log.error("Invalid UUID format: {}", examId, e);
            return "<html><body><h1>错误</h1><p>无效的试卷ID格式</p></body></html>";
        } catch (Exception e) {
            log.error("Error getting separate answer HTML for examId: {}", examId, e);
            return "<html><body><h1>错误</h1><p>获取题目和答案失败: " + e.getMessage() + "</p></body></html>";
        }
    }

    /**
     * 获取数学专项训练纯答案的 HTML
     *
     * @param examId 试卷ID
     * @return 纯答案的HTML内容
     */
    @Operation(summary = "获取数学专项训练纯答案的 HTML", description = "根据试卷ID获取数学专项训练的纯答案 HTML 内容")
    @PostMapping(value = "/html/answers", produces = MediaType.TEXT_HTML_VALUE)
    public String getMathTrainingAnswersOnlyHtml(@RequestParam(value = "examId", required = true) String examIdStr) {
        try {
            UUID examId = UUID.fromString(examIdStr);
            String html = aiAbilityService.mathSpecialTrainingAnswersOnly(examId);
            return html;
        } catch (IllegalArgumentException e) {
            log.error("Invalid UUID format: {}", examIdStr, e);
            return "<html><body><h1>错误</h1><p>无效的试卷ID格式</p></body></html>";
        } catch (Exception e) {
            log.error("Error getting answers-only HTML for examId: {}", examIdStr, e);
            return "<html><body><h1>错误</h1><p>获取纯答案失败: " + e.getMessage() + "</p></body></html>";
        }
    }
}
