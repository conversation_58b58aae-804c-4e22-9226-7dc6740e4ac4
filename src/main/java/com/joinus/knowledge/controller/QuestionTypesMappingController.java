package com.joinus.knowledge.controller;

import com.joinus.knowledge.common.Result;
import com.joinus.knowledge.model.entity.MathQuestionType;
import com.joinus.knowledge.model.entity.MathQuestion;
import com.joinus.knowledge.model.vo.MathQuestionTypeVO;
import com.joinus.knowledge.service.MathQuestionTypesService;
import com.joinus.knowledge.service.MathQuestionsService;
import com.joinus.knowledge.service.QuestionTypesMappingService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * 题目-题型关联关系管理 Controller
 */
@RestController
@RequestMapping("/api/question-types-mapping")
@RequiredArgsConstructor
public class QuestionTypesMappingController {

    private final QuestionTypesMappingService questionTypesMappingService;
    private final MathQuestionsService mathQuestionsService;
    private final MathQuestionTypesService mathQuestionTypesService;

    /**
     * 根据题目ID获取关联的所有题型
     * GET /api/question-types-mapping/question/{questionId}
     */
    @GetMapping("/question/{questionId}")
    public Result<List<MathQuestionTypeVO>> getQuestionTypesByQuestionId(@PathVariable("questionId") UUID questionId) {
        // 获取题型ID列表
        List<UUID> questionTypeIds = questionTypesMappingService.listQuestionTypeIdsByQuestionId(questionId);
        
        if (questionTypeIds.isEmpty()) {
            return Result.success(List.of());
        }
        
        // 查询题型信息
        List<MathQuestionTypeVO> questionTypes = mathQuestionTypesService.listByIds(questionTypeIds);
        return Result.success(questionTypes);
    }

    /**
     * 根据题型ID获取关联的所有题目
     * GET /api/question-types-mapping/question-type/{questionTypeId}
     */
    @GetMapping("/question-type/{questionTypeId}")
    public Result<List<MathQuestion>> getQuestionsByQuestionTypeId(@PathVariable UUID questionTypeId) {
        // 获取题目ID列表
        List<UUID> questionIds = questionTypesMappingService.listQuestionIdsByQuestionTypeId(questionTypeId);
        
        if (questionIds.isEmpty()) {
            return Result.success(List.of());
        }
        
        // 查询题目信息
        List<MathQuestion> questions = mathQuestionsService.listByIds(questionIds);
        return Result.success(questions);
    }

    /**
     * 批量关联题型到一个题目
     * POST /api/question-types-mapping/question/{questionId}
     * Request body: ["questionTypeId1", "questionTypeId2", ...]
     */
    @PostMapping("/question/{questionId}")
    public Result<Void> associateQuestionTypesWithQuestion(@PathVariable UUID questionId, @RequestBody List<UUID> questionTypeIds) {
        boolean success = questionTypesMappingService.batchCreateAssociationsByQuestionId(questionId, questionTypeIds);
        return success ? Result.success() : Result.error("关联题型失败");
    }

    /**
     * 批量关联题目到一个题型
     * POST /api/question-types-mapping/question-type/{questionTypeId}
     * Request body: ["questionId1", "questionId2", ...]
     */
    @PostMapping("/question-type/{questionTypeId}")
    public Result<Void> associateQuestionsWithQuestionType(@PathVariable UUID questionTypeId, @RequestBody List<UUID> questionIds) {
        boolean success = questionTypesMappingService.batchCreateAssociationsByQuestionTypeId(questionTypeId, questionIds);
        return success ? Result.success() : Result.error("关联题目失败");
    }

    /**
     * 创建单个关联关系
     * POST /api/question-types-mapping
     * Request body: {"questionId": "uuid", "questionTypeId": "uuid"}
     */
    @PostMapping
    public Result<Void> createAssociation(@RequestBody Map<String, UUID> request) {
        UUID questionId = request.get("questionId");
        UUID questionTypeId = request.get("questionTypeId");
        
        if (questionId == null || questionTypeId == null) {
            return Result.error("题目ID和题型ID不能为空");
        }
        
        boolean success = questionTypesMappingService.createAssociation(questionId, questionTypeId);
        return success ? Result.success() : Result.error("创建关联失败");
    }
    
    /**
     * 批量创建题目与题型的关联关系
     * POST /api/question-types-mapping/batch
     * Request body: [{"questionTypeId": "uuid", "questionId": "uuid"}, ...]
     */
    @PostMapping("/batch")
    public Result<Void> batchCreateAssociations(@RequestBody List<Map<String, String>> relationsList) {
        if (relationsList == null || relationsList.isEmpty()) {
            return Result.error("关系列表不能为空");
        }
        
        boolean success = true;
        for (Map<String, String> relation : relationsList) {
            String questionTypeIdStr = relation.get("questionTypeId");
            String questionIdStr = relation.get("questionId");
            
            if (questionTypeIdStr == null || questionIdStr == null) {
                return Result.error("题型ID和题目ID不能为空");
            }
            
            try {
                UUID questionTypeId = UUID.fromString(questionTypeIdStr);
                UUID questionId = UUID.fromString(questionIdStr);
                
                boolean result = questionTypesMappingService.createAssociation(questionId, questionTypeId);
                if (!result) {
                    success = false;
                }
            } catch (IllegalArgumentException e) {
                return Result.error("无效的UUID格式");
            }
        }
        
        return success ? Result.success() : Result.error("部分关联关系创建失败");
    }

    /**
     * 删除单个关联关系
     * DELETE /api/question-types-mapping
     * Request body: {"questionId": "uuid", "questionTypeId": "uuid"}
     */
    @DeleteMapping
    public Result<Void> deleteAssociation(@RequestBody Map<String, UUID> request) {
        UUID questionId = request.get("questionId");
        UUID questionTypeId = request.get("questionTypeId");
        
        if (questionId == null || questionTypeId == null) {
            return Result.error("题目ID和题型ID不能为空");
        }
        
        boolean success = questionTypesMappingService.deleteAssociation(questionId, questionTypeId);
        return success ? Result.success() : Result.error("删除关联失败");
    }

    /**
     * 删除题目的所有关联
     * DELETE /api/question-types-mapping/question/{questionId}
     */
    @DeleteMapping("/question/{questionId}")
    public Result<Void> deleteAssociationsByQuestionId(@PathVariable UUID questionId) {
        boolean success = questionTypesMappingService.deleteAssociationsByQuestionId(questionId);
        return success ? Result.success() : Result.error("删除题目关联失败");
    }

    /**
     * 删除题型的所有关联
     * DELETE /api/question-types-mapping/question-type/{questionTypeId}
     */
    @DeleteMapping("/question-type/{questionTypeId}")
    public Result<Void> deleteAssociationsByQuestionTypeId(@PathVariable UUID questionTypeId) {
        boolean success = questionTypesMappingService.deleteAssociationsByQuestionTypeId(questionTypeId);
        return success ? Result.success() : Result.error("删除题型关联失败");
    }
}
