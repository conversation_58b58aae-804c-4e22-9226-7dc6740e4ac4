package com.joinus.knowledge.controller;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.aliyun.ocr_api20210707.models.RecognizeEduPaperStructedResponse;
import com.joinus.knowledge.common.Result;
import com.joinus.knowledge.enums.PaperSubjectType;
import com.joinus.knowledge.model.param.ImageParam;
import com.joinus.knowledge.service.ImageRecognizeService;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/image/recognize")
public class ImageRecognzieController {

    @Resource
    private ImageRecognizeService imageRecognizeService;

    @PostMapping("/paper-structed")
    public Result<RecognizeEduPaperStructedResponse> paperStructed(@RequestBody ImageParam imageParam) {
        RecognizeEduPaperStructedResponse recognizeEduPaperStructedResponse = imageRecognizeService.paperStructed(imageParam.getImageUrl(), PaperSubjectType.JUNIOR_HIGH_SCHOOL_MATH.getType());
        return Result.success(recognizeEduPaperStructedResponse);
    }
}
