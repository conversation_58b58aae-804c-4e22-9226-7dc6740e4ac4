package com.joinus.knowledge.controller;

import com.joinus.knowledge.common.Result;
import com.joinus.knowledge.model.entity.MathExamPoints;
import com.joinus.knowledge.model.entity.MathQuestion;
import com.joinus.knowledge.service.MathExamPointsService;
import com.joinus.knowledge.service.MathQuestionsService;
import com.joinus.knowledge.service.QuestionExamPointsService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * 题目-考点关联关系管理 Controller
 */
@RestController
@RequestMapping("/api/question-exam-points")
@RequiredArgsConstructor
public class QuestionExamPointsController {

    private final QuestionExamPointsService questionExamPointsService;
    private final MathQuestionsService mathQuestionsService;
    private final MathExamPointsService mathExamPointsService;

    /**
     * 根据题目ID获取关联的所有考点
     * GET /api/question-exam-points/question/{questionId}
     */
    @GetMapping("/question/{questionId}")
    public Result<List<MathExamPoints>> getExamPointsByQuestionId(@PathVariable UUID questionId) {
        // 获取考点ID列表
        List<UUID> examPointIds = questionExamPointsService.getExamPointIdsByQuestionId(questionId);
        
        if (examPointIds.isEmpty()) {
            return Result.success(List.of());
        }
        
        // 查询考点信息
        List<MathExamPoints> examPoints = mathExamPointsService.listByIds(examPointIds);
        return Result.success(examPoints);
    }

    /**
     * 根据考点ID获取关联的所有题目
     * GET /api/question-exam-points/exam-point/{examPointId}
     */
    @GetMapping("/exam-point/{examPointId}")
    public Result<List<MathQuestion>> getQuestionsByExamPointId(@PathVariable UUID examPointId) {
        // 获取题目ID列表
        List<UUID> questionIds = questionExamPointsService.getQuestionIdsByExamPointId(examPointId);
        
        if (questionIds.isEmpty()) {
            return Result.success(List.of());
        }
        
        // 查询题目信息
        List<MathQuestion> questions = mathQuestionsService.listByIds(questionIds);
        return Result.success(questions);
    }

    /**
     * 批量关联考点到一个题目
     * POST /api/question-exam-points/question/{questionId}
     * Request body: ["examPointId1", "examPointId2", ...]
     */
    @PostMapping("/question/{questionId}")
    public Result<Void> associateExamPointsWithQuestion(@PathVariable UUID questionId, @RequestBody List<UUID> examPointIds) {
        boolean success = questionExamPointsService.batchCreateAssociationsByQuestionId(questionId, examPointIds);
        return success ? Result.success() : Result.error("关联考点失败");
    }

    /**
     * 批量关联题目到一个考点
     * POST /api/question-exam-points/exam-point/{examPointId}
     * Request body: ["questionId1", "questionId2", ...]
     */
    @PostMapping("/exam-point/{examPointId}")
    public Result<Void> associateQuestionsWithExamPoint(@PathVariable UUID examPointId, @RequestBody List<UUID> questionIds) {
        boolean success = questionExamPointsService.batchCreateAssociationsByExamPointId(examPointId, questionIds);
        return success ? Result.success() : Result.error("关联题目失败");
    }

    /**
     * 创建单个关联关系
     * POST /api/question-exam-points
     * Request body: {"questionId": "uuid", "examPointId": "uuid"}
     */
    @PostMapping
    public Result<Void> createAssociation(@RequestBody Map<String, UUID> request) {
        UUID questionId = request.get("questionId");
        UUID examPointId = request.get("examPointId");
        
        if (questionId == null || examPointId == null) {
            return Result.error("题目ID和考点ID不能为空");
        }
        
        boolean success = questionExamPointsService.createAssociation(questionId, examPointId);
        return success ? Result.success() : Result.error("创建关联失败");
    }
    
    /**
     * 批量创建题目与考点的关联关系
     * POST /api/question-exam-points/batch
     * Request body: [{"examPointId": "uuid", "questionId": "uuid"}, ...]
     */
    @PostMapping("/batch")
    public Result<Void> batchCreateAssociations(@RequestBody List<Map<String, String>> relationsList) {
        if (relationsList == null || relationsList.isEmpty()) {
            return Result.error("关系列表不能为空");
        }
        
        boolean success = true;
        for (Map<String, String> relation : relationsList) {
            String examPointIdStr = relation.get("examPointId");
            String questionIdStr = relation.get("questionId");
            
            if (examPointIdStr == null || questionIdStr == null) {
                return Result.error("考点ID和题目ID不能为空");
            }
            
            try {
                UUID examPointId = UUID.fromString(examPointIdStr);
                UUID questionId = UUID.fromString(questionIdStr);
                
                boolean result = questionExamPointsService.createAssociation(questionId, examPointId);
                if (!result) {
                    success = false;
                }
            } catch (IllegalArgumentException e) {
                return Result.error("无效的UUID格式");
            }
        }
        
        return success ? Result.success() : Result.error("部分关联关系创建失败");
    }

    /**
     * 删除单个关联关系
     * DELETE /api/question-exam-points
     * Request body: {"questionId": "uuid", "examPointId": "uuid"}
     */
    @DeleteMapping
    public Result<Void> deleteAssociation(@RequestBody Map<String, UUID> request) {
        UUID questionId = request.get("questionId");
        UUID examPointId = request.get("examPointId");
        
        if (questionId == null || examPointId == null) {
            return Result.error("题目ID和考点ID不能为空");
        }
        
        boolean success = questionExamPointsService.deleteAssociation(questionId, examPointId);
        return success ? Result.success() : Result.error("删除关联失败");
    }

    /**
     * 删除题目的所有关联
     * DELETE /api/question-exam-points/question/{questionId}
     */
    @DeleteMapping("/question/{questionId}")
    public Result<Void> deleteAssociationsByQuestionId(@PathVariable UUID questionId) {
        boolean success = questionExamPointsService.deleteAssociationsByQuestionId(questionId);
        return success ? Result.success() : Result.error("删除题目关联失败");
    }

    /**
     * 删除考点的所有关联
     * DELETE /api/question-exam-points/exam-point/{examPointId}
     */
    @DeleteMapping("/exam-point/{examPointId}")
    public Result<Void> deleteAssociationsByExamPointId(@PathVariable UUID examPointId) {
        boolean success = questionExamPointsService.deleteAssociationsByExamPointId(examPointId);
        return success ? Result.success() : Result.error("删除考点关联失败");
    }
}
