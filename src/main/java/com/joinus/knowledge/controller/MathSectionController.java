package com.joinus.knowledge.controller;

import cn.hutool.core.lang.Assert;
import com.joinus.knowledge.common.Result;
import com.joinus.knowledge.enums.PublisherType;
import com.joinus.knowledge.model.param.*;
import com.joinus.knowledge.model.vo.MathChapterVO;
import com.joinus.knowledge.model.vo.MathSectionVO;
import com.joinus.knowledge.model.vo.SectionKeypointVO;
import com.joinus.knowledge.service.MathSectionService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.UUID;

@RestController
@RequestMapping("/sections")
@RequiredArgsConstructor
public class MathSectionController {

    private final MathSectionService mathSectionService;


    @GetMapping("/{sectionId}/keypoints")
    public Result<List<SectionKeypointVO> > listKeypoints(@PathVariable("sectionId") UUID sectionId) {
        List<SectionKeypointVO> list = mathSectionService.listKeypointsById(sectionId);
        return Result.success(list);
    }


    @PutMapping("/keypoints")
    public Result<Void> updateSectionKeypointMapping(@Valid @RequestBody UpdateSectionKeypointParam param) {
        Assert.isTrue(null != param.getOldSectionId(), "oldSectionId不能为空");
        mathSectionService.updateSectionKeypointMapping(param);
        return Result.success();
    }

    @DeleteMapping("/keypoints")
    public Result<Void> deleteSectionKeypointMapping(@Valid @RequestBody DeleteSectionKeypointParam param) {
        mathSectionService.deleteSectionKeypointMapping(param);
        return Result.success();
    }

    @PutMapping("/keypoints/switch-type")
    public Result<Void> switchKeypointType(@Valid @RequestBody SwitchKeypointTypeParam param) {
        mathSectionService.switchKeypointType(param);
        return Result.success();
    }

    @PutMapping("/keypoints/page-index")
    public Result<Void> updatePageIndex(@Valid @RequestBody UpdateSectionKeypointParam param) {
        Assert.isTrue(null != param.getPageIndex(), "pageIndex不能为空");
        mathSectionService.updatePageIndex(param);
        return Result.success();
    }

    @PostMapping("/keypoints/combine")
    public Result<UUID> combineKeypoints(@Valid @RequestBody CombineKeypointParam param) {
        UUID id = mathSectionService.combineKeypoints(param);
        return Result.success(id);
    }

    @PostMapping("/keypoints")
    public Result<String> addKeypoints(@Valid @RequestBody AddSectionKeypointParam param) {
        mathSectionService.addKeypoints(param);
        return Result.success("success");
    }


    @PostMapping("/knowledge-points/seperate")
    public Result<String> separateKnowledgePoints() {
        mathSectionService.seperateKnowledgePoints();
        return Result.success("success");
    }

    @PostMapping("/question-types/seperate")
    public Result<String> separateQuestionTypes() {
        mathSectionService.seperateQuestionTypes();
        return Result.success("success");
    }

    @GetMapping
    public Result<List<MathSectionVO>> list(@RequestParam(value = "name", required = false) String name,
                                            @RequestParam(value = "grade", required = false) Integer grade,
                                            @RequestParam(value = "semester", required = false) Integer semester,
                                            @RequestParam(value = "publisher", required = false) PublisherType publisher,
                                            @RequestParam(value = "chapterName", required = false) String chapterName,
                                            @RequestParam(value = "chapterId", required = false) UUID chapterId) {
        List<MathSectionVO> list = mathSectionService.list(name, grade, semester, publisher, chapterName, chapterId);
        return Result.success(list);
    }

}
