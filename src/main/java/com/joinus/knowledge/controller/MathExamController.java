package com.joinus.knowledge.controller;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.joinus.knowledge.common.Result;
import com.joinus.knowledge.config.base.BusinessException;
import com.joinus.knowledge.enums.ExamSourceType;
import com.joinus.knowledge.enums.ExamStateEnum;
import com.joinus.knowledge.enums.RegularExamType;
import com.joinus.knowledge.model.dto.AddExamQuestionParam;
import com.joinus.knowledge.model.entity.MathExam;
import com.joinus.knowledge.model.entity.MathExamQuestion;
import com.joinus.knowledge.model.param.*;
import com.joinus.knowledge.model.po.MathKnowledgePointPO;
import com.joinus.knowledge.model.vo.FileVO;
import com.joinus.knowledge.model.vo.MathExamQuestionVO;
import com.joinus.knowledge.model.vo.MathExamVO;
import com.joinus.knowledge.model.vo.QuestionDetailVO;
import com.joinus.knowledge.model.po.MathKnowledgePointPO;
import com.joinus.knowledge.model.vo.*;
import com.joinus.knowledge.service.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.*;
import java.util.concurrent.CompletableFuture;

/**
 * 试卷处理接口
 */
@Tag(name = "数学试卷处理接口", description = "提供数学试卷录入相关功能")
@RestController
@RequestMapping("/math/exams")
@RequiredArgsConstructor
@Slf4j
public class MathExamController {

    private final MathExamsService mathExamsService;
    @Resource
    private AIAbilityService aiAbilityService;
    @Resource
    private MathExamQuestionsService mathExamQuestionsService;
    @Resource
    private QuestionFileService questionFileService;
    @Resource
    private FilesService filesService;
    @Resource
    private ImageRecognizeService imageRecognizeService;
    @Autowired
    private MathExamFilesService mathExamFilesService;
    @Resource
    private TemporalWorkflowService temporalWorkflowService;
    @Resource
    private MathKnowledgePointsService mathKnowledgePointsService;
    @Resource
    private MathExamTagsService mathExamTagsService;

    @Operation(summary = "试卷上传")
    @PostMapping("/upload")
    public Result<UUID> uploadMathExam(@RequestBody CutExamParam param) {

        UUID examId = aiAbilityService.uploadMathExam(param);

        return ocrMathExam(examId);
    }

    @Operation(summary = "根据题目ids创建数学试卷")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "分页查询成功",
                    content = @Content(mediaType = "application/json", schema = @Schema(implementation = UUID.class))),
            @ApiResponse(responseCode = "400", description = "请求参数错误"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @PostMapping("/by-questions")
    public Result<UUID> createMathExam(@RequestBody @Valid CreateExamParam param) {
        UUID examId = mathExamsService.createMathExam(param);
        return Result.success(examId);
    }

    @Operation(summary = "试卷ocr")
    @PostMapping("/ocr")
    public Result<UUID> ocrMathExam(@RequestParam("examId") UUID examId) {

        CompletableFuture.runAsync(() -> {
            MathExamVO mathExamVO = mathExamsService.queryExamDetailById(examId);
            mathExamVO.getImages().forEach(fileVO -> imageRecognizeService.ocrHtml(fileVO.getOssUrl(), fileVO.getId()));
            MathExam mathExam = new MathExam();
            mathExam.setId(examId);
            mathExam.setState(ExamStateEnum.AUTO_RECOGNIZED);
            mathExamsService.updateById(mathExam);
        });

        return Result.success(examId);
    }

    /**
     * 试卷分页查询接口
     * GET /api/math/exams/page
     * 支持条件：试卷名、状态、教材版本、年级、学期
     */
    @Operation(
        summary = "试卷分页查询"
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "分页查询成功",
                content = @Content(mediaType = "application/json", schema = @Schema(implementation = MathExamVO.class))),
        @ApiResponse(responseCode = "400", description = "请求参数错误"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @GetMapping("/page")
    public Result<IPage<MathExamVO>> pageQuery(@ParameterObject MathExamPageQueryParam param) {
        Page<MathExamVO> pageObj = new Page<>(param.getPage(), param.getSize());
        IPage<MathExamVO> result = mathExamsService.pageQuery(pageObj, param);

        return Result.success(result);
    }


    @Operation(summary = "获取试卷详情")
    @Parameters({
            @Parameter(name = "id", description = "试卷ID", required = true, example = "8653cbe2-fc68-406c-8bf6-ae7e85e887aa")
    })
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "分页查询成功",
                    content = @Content(mediaType = "application/json", schema = @Schema(implementation = FileVO.class))),
            @ApiResponse(responseCode = "400", description = "请求参数错误"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @GetMapping("/{id}")
    public Result<MathExamVO> queryExamDetail(@PathVariable("id") UUID id) {
        MathExamVO mathExamVO = mathExamsService.queryExamDetailById(id);
        return Result.success(mathExamVO);
    }

    @Operation(summary = "修改试卷-主要是修改试卷的审核状态")
    @Parameters({
            @Parameter(name = "id", description = "试卷ID", required = true, example = "8653cbe2-fc68-406c-8bf6-ae7e85e887aa")
    })
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "分页查询成功",
                    content = @Content(mediaType = "application/json", schema = @Schema(implementation = FileVO.class))),
            @ApiResponse(responseCode = "400", description = "请求参数错误"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @PutMapping("/{id}")
    public Result<MathExamVO> updateExamDetail(@PathVariable("id") UUID id,
                                               @RequestBody UpdateMathExamParam param) {
        MathExamVO mathExamVO = mathExamsService.updateExamDetail(id, param);
        if (null != param.getState() && param.getState() == ExamStateEnum.HUMAN_RECOGNIZED) {
            analyzeMathExamAsync(id);
        }
        return Result.success(mathExamVO);
    }


    @PutMapping("/{id}/state")
    public Result<MathExamVO> updateExamState(@PathVariable("id") UUID id,
                                               @RequestBody UpdateMathExamParam param) {
        mathExamsService.updateExamState(id, param);
        return Result.success();
    }

    @PostMapping("/{id}/tags-and-info")
    public Result<MathExamVO> updateExam(@PathVariable("id") UUID id,
                                               @RequestBody UpdateMathExamParam param) {
        MathExamVO mathExamVO = mathExamsService.updateExamInfo(id, param);
        if (null != param.getState() && param.getState() == ExamStateEnum.HUMAN_RECOGNIZED) {
            analyzeMathExamAsync(id);
        }
        return Result.success(mathExamVO);
    }

    @GetMapping("/{id}/tags-and-info")
    public Result<MathExamVO> getExamTagsAndInfo(@PathVariable("id") UUID id) {
        MathExamVO mathExamVO = mathExamsService.getExamTagsAndInfo(id);
        return Result.success(mathExamVO);
    }

    @GetMapping("/{id}/schools")
    public Result<List<SchoolVO>> updateExam(@PathVariable("id") UUID id) {
        List<SchoolVO> schoolVOs = mathExamsService.listSchoolByExamId(id);
        return Result.success(schoolVOs);
    }

    @Operation(summary = "获取试卷题目列表")
    @Parameters({
            @Parameter(name = "id", description = "试卷ID", required = true, example = "8653cbe2-fc68-406c-8bf6-ae7e85e887aa")
    })
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "分页查询成功",
                    content = @Content(mediaType = "application/json", schema = @Schema(implementation = MathExamQuestionVO.class))),
            @ApiResponse(responseCode = "400", description = "请求参数错误"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @GetMapping("/{id}/questions")
    public Result<List<MathExamQuestionVO>> listMathExamQuestions(@PathVariable("id") UUID id) {
        List<MathExamQuestionVO> questions = mathExamsService.listMathExamQuestions(id);
        return Result.success(questions);
    }

    @Operation(summary = "添加试卷题目")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "分页查询成功",
                    content = @Content(mediaType = "application/json", schema = @Schema(implementation = QuestionDetailVO.class))),
            @ApiResponse(responseCode = "400", description = "请求参数错误"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @PostMapping("/{id}/questions")
    public Result<QuestionDetailVO> addMathExamQuestion(@PathVariable("id") UUID examId,
                                                        @RequestBody AddExamQuestionParam param) {
        QuestionDetailVO question = mathExamsService.addMathExamQuestion(examId, param);
        return Result.success(question);
    }

    @Operation(summary = "修改试卷题目")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "分页查询成功",
                    content = @Content(mediaType = "application/json", schema = @Schema(implementation = QuestionDetailVO.class))),
            @ApiResponse(responseCode = "400", description = "请求参数错误"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @PutMapping("/{id}/questions/{questionId}")
    public Result<QuestionDetailVO> updateMathExamQuestion(@PathVariable("id") UUID examId,
                                                           @PathVariable("questionId") UUID questionId,
                                                           @RequestBody AddExamQuestionParam param) {
        QuestionDetailVO question = mathExamsService.updateMathExamQuestion(examId, questionId, param);
        return Result.success(question);
    }

    @Operation(summary = "删除试卷题目")
    @Parameters({
            @Parameter(name = "id", description = "试卷ID", required = true, example = "8653cbe2-fc68-406c-8bf6-ae7e85e887aa"),
            @Parameter(name = "questionId", description = "题目ID", required = true, example = "8653cbe2-fc68-406c-8bf6-ae7e85e887aa")
    })
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "分页查询成功",
                    content = @Content(mediaType = "application/json", schema = @Schema(implementation = Boolean.class))),
            @ApiResponse(responseCode = "400", description = "请求参数错误"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @DeleteMapping("/{id}/questions/{questionId}")
    public Result<Boolean> deleteMathExamQuestion(@PathVariable("id") UUID examId,
                                                  @PathVariable("questionId") UUID questionId) {
        mathExamsService.deleteMathExamQuestion(examId, questionId);
        return Result.success(true);
    }



    @Operation(summary = "试卷分析")
    @PostMapping("/analyze")
    public Result<String> analyzeMathExam(@Valid @RequestBody AnalyzeExamParam param) {
        analyzeMathExamAsync(param.getExamId());
        return Result.success("分析中...");
    }

    @Operation(summary = "通过excel导入修改试卷属性")
    @PostMapping("/import/excel")
    public Result<String> updateExamByExcel(@RequestParam("file") MultipartFile file) {
        try {
            // 1. 验证文件
            if (file.isEmpty()) {
                return Result.error("上传的文件不能为空");
            }

            String fileName = file.getOriginalFilename();
            if (fileName == null || (!fileName.endsWith(".xls") && !fileName.endsWith(".xlsx"))) {
                return Result.error("请上传有效的Excel文件(.xls 或 .xlsx)");
            }

            // 2. 解析Excel文件
            List<UpdateExamByExcelParam> dataList = com.joinus.knowledge.config.excel.ExcelConverterUtils
                    .createReaderWithCommonConverters(file.getInputStream(), UpdateExamByExcelParam.class)
                    .sheet()
                    .doReadSync();

            if (dataList == null || dataList.isEmpty()) {
                return Result.error("Excel文件中没有数据");
            }
            mathExamsService.updateExamByExcel(dataList);
        } catch (Exception e) {
            // 记录详细的错误信息
            log.error("Excel文件解析失败", e);

            // 根据异常类型提供更具体的错误信息
            String errorMessage = "文件解析失败";
            errorMessage = "类初始化失败，可能是依赖库版本冲突或配置问题";
            Throwable cause = e.getCause();
            if (cause != null) {
                errorMessage += ": " + cause.getMessage();
            }
            throw new BusinessException(errorMessage);
        }
        return Result.success();
    }

    private void analyzeMathExamAsync(UUID examId) {

        MathExam mathExam = mathExamsService.getById(examId);
        if (mathExam.getName().contains("薄弱点专项训练试卷")) {
            throw new BusinessException("专项训练试卷不支持分析");
        }

        AnalyzeExamParam examParam = AnalyzeExamParam.builder()
                .examId(examId)
                .publisher(mathExam.getPublisher())
                .build();
        CompletableFuture.runAsync(() -> temporalWorkflowService.analyzeKnowledgePointsForExam(examParam));
    }

    @Operation(summary = "试卷来源字典")
    @GetMapping("/source-types")
    public Result<List<Map<String, String>>> listExamSourceTypes() {
        List<Map<String, String>> result = new ArrayList<>();
        for (ExamSourceType type : ExamSourceType.values()) {
            HashMap<String, String> map = new HashMap<>();
            map.put("key", type.name());
            map.put("value", type.getName());
            result.add(map);
        }
        return Result.success(result);
    }

    @Operation(summary = "试卷状态字典")
    @GetMapping("/state-types")
    public Result<List<Map<String, String>>> listExamStateTypes() {
        List<Map<String, String>> result = new ArrayList<>();
        for (ExamStateEnum type : ExamStateEnum.values()) {
            HashMap<String, String> map = new HashMap<>();
            map.put("key", type.name());
            map.put("value", type.getDesc());
            result.add(map);
        }
        return Result.success(result);
    }

    @Operation(summary = "试卷审核")
    @PostMapping("/review")
    public Result<ExamStateEnum> reviewMathExam(@RequestBody ReviewMathExamParam param) {
        return Result.success(mathExamsService.reviewMathExam(param));
    }

    @Operation(summary = "常规试卷类型字典")
    @GetMapping("/regular-exam-types")
    public Result<List<Map<String, String>>> listRegularExamTypes() {
        List<Map<String, String>> result = new ArrayList<>();
        for (RegularExamType type : RegularExamType.values()) {
            HashMap<String, String> map = new HashMap<>();
            map.put("key", type.name());
            map.put("value", type.getDesc());
            result.add(map);
        }
        return Result.success(result);
    }

    @PostMapping("/{examId}/tags")
    public Result<String> addExamTags(@PathVariable("examId") UUID examId,
                                      @RequestBody List<AddExamTagParam> param) {
        mathExamTagsService.addExamTags(examId, param);
        return Result.success();
    }

    @Operation(summary = "设置试卷主要别名")
    @PostMapping("/{examId}/primary-alias")
    public Result<String> setPrimaryAlias(@PathVariable("examId") UUID examId,
                                          @RequestBody SetPrimaryAliasParam param) {
        mathExamTagsService.setPrimaryAlias(examId, param.getAliasValue(), param.getProperties());
        return Result.success();
    }

    @Operation(summary = "通过excel导入新增试卷标签")
    @PostMapping("/tags/import/excel")
    public Result<String> addExamTagsByExcel(@RequestParam("file") MultipartFile file) {
        try {
            // 1. 验证文件
            if (file.isEmpty()) {
                return Result.error("上传的文件不能为空");
            }

            String fileName = file.getOriginalFilename();
            if (fileName == null || (!fileName.endsWith(".xls") && !fileName.endsWith(".xlsx"))) {
                return Result.error("请上传有效的Excel文件(.xls 或 .xlsx)");
            }

            // 2. 解析Excel文件
            List<AddExamTagByExcelParam> dataList = com.joinus.knowledge.config.excel.ExcelConverterUtils
                    .createReaderWithCommonConverters(file.getInputStream(), AddExamTagByExcelParam.class)
                    .sheet()
                    .doReadSync();

            if (dataList == null || dataList.isEmpty()) {
                return Result.error("Excel文件中没有数据");
            }
            mathExamTagsService.addExamTagsByExcel(dataList);
        } catch (Exception e) {
            // 记录详细的错误信息
            log.error("Excel文件解析失败", e);

            // 根据异常类型提供更具体的错误信息
            String errorMessage = "文件解析失败";
            errorMessage = "类初始化失败，可能是依赖库版本冲突或配置问题";
            Throwable cause = e.getCause();
            if (cause != null) {
                errorMessage += ": " + cause.getMessage();
            }
            throw new BusinessException(errorMessage);
        }
        return Result.success();
    }
}
