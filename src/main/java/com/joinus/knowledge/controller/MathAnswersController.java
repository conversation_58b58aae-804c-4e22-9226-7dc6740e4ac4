package com.joinus.knowledge.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.joinus.knowledge.common.Result;
import com.joinus.knowledge.model.entity.MathAnswer;
import com.joinus.knowledge.model.param.UpdateQuestionAnswerParam;
import com.joinus.knowledge.model.vo.QuestionAnswerDetailVO;
import com.joinus.knowledge.service.MathAnswersService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.UUID;

/**
 * 数学题目答案管理 Controller
 */
@RestController
@RequestMapping("/math/question-answers")
@RequiredArgsConstructor
public class MathAnswersController {

    private final MathAnswersService mathAnswersService;

    /**
     * 查询所有题目答案
     * GET /api/math/question-answers
     */
    @GetMapping
    public Result<List<MathAnswer>> list() {
        return Result.success(mathAnswersService.list());
    }

    /**
     * 分页查询题目答案
     * GET /api/math/question-answers/page?page=1&size=10
     */
    @GetMapping("/page")
    public Result<Page<MathAnswer>> page(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) String answer) {
        
        Page<MathAnswer> pageParam = new Page<>(page, size);
        LambdaQueryWrapper<MathAnswer> queryWrapper = new LambdaQueryWrapper<>();
        
        // 添加查询条件
        if (answer != null && !answer.isEmpty()) {
            queryWrapper.like(MathAnswer::getAnswer, answer);
        }
        
        Page<MathAnswer> resultPage = mathAnswersService.page(pageParam, queryWrapper);
        return Result.success(resultPage);
    }

    /**
     * 根据ID查询题目答案
     * GET /api/math/question-answers/{id}
     */
    @GetMapping("/{id}")
    public Result<QuestionAnswerDetailVO> getById(@PathVariable UUID id) {
        QuestionAnswerDetailVO questionAnswerDetailVO = mathAnswersService.getById(id);
        if (questionAnswerDetailVO == null) {
            return Result.error(404, "题目答案不存在");
        }
        return Result.success(questionAnswerDetailVO);
    }

    /**
     * 创建题目答案
     * POST /api/math/question-answers
     */
    @PostMapping
    public Result<MathAnswer> create(@RequestBody MathAnswer questionAnswer) {
        // 生成UUID
        questionAnswer.generateUUID();
        
        boolean success = mathAnswersService.saveAnswer(questionAnswer);
        if (success) {
            return Result.success(questionAnswer);
        }
        return Result.error("创建题目答案失败");
    }

    @PutMapping("/question/{questionId}")
    public Result<MathAnswer> create(@PathVariable("questionId") UUID questionId, @RequestBody MathAnswer questionAnswer) {
        // 生成UUID
        questionAnswer.generateUUID();

        boolean success = mathAnswersService.saveAnswerAndRelation(questionId, questionAnswer);
        if (success) {
            return Result.success(questionAnswer);
        }
        return Result.error("创建题目答案失败");
    }

    /**
     * 批量创建题目答案
     * POST /api/math/question-answers/batch
     */
    @PostMapping("/batch")
    public Result<Boolean> batchCreate(@RequestBody List<MathAnswer> questionAnswersList) {
        if (questionAnswersList == null || questionAnswersList.isEmpty()) {
            return Result.error("题目答案列表不能为空");
        }
        
        // 为每个题目答案生成UUID
        questionAnswersList.forEach(MathAnswer::generateUUID);
        
        // 批量保存题目答案
        boolean success = mathAnswersService.saveBatch(questionAnswersList);
        
        if (success) {
            return Result.success(true);
        }
        return Result.error("批量创建题目答案失败");
    }
    
    /**
     * 更新题目答案
     * PUT /api/math/question-answers/{id}
     */
    @PutMapping("/{id}")
    public Result<QuestionAnswerDetailVO> update(@PathVariable("id") UUID id, @RequestBody UpdateQuestionAnswerParam param) {
        // 确保要更新的ID正确
        QuestionAnswerDetailVO questionAnswerDetailVO = mathAnswersService.updateById(id, param);
        return Result.success(questionAnswerDetailVO);
    }

    /**
     * 删除题目答案
     * DELETE /api/math/question-answers/{id}
     */
    @DeleteMapping("/{id}")
    public Result<Boolean> delete(@PathVariable("id") UUID id) {
        boolean success = mathAnswersService.removeById(id);
        if (success) {
            return Result.success(true);
        }
        return Result.error("删除题目答案失败");
    }
}
