package com.joinus.knowledge.config.typehandler;

import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;

import java.sql.*;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

@MappedTypes(List.class)
@MappedJdbcTypes(JdbcType.ARRAY)
public class StringListTypeHandler extends BaseTypeHandler<List<String>> {

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, List<String> parameter, JdbcType jdbcType) throws SQLException {
        // 使用 connection.createArrayOf 方法创建 SQL Array
        Connection conn = ps.getConnection();
        // 第一个参数是 SQL 类型名称 ("text" for TEXT, "varchar" for VARCHAR)
        // 第二个参数是 Java 数组
        Array sqlArray = conn.createArrayOf("text", parameter.toArray());
        ps.setArray(i, sqlArray);
    }

    @Override
    public List<String> getNullableResult(ResultSet rs, String columnName) throws SQLException {
        Array sqlArray = rs.getArray(columnName);
        return arrayToList(sqlArray);
    }

    @Override
    public List<String> getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        Array sqlArray = rs.getArray(columnIndex);
        return arrayToList(sqlArray);
    }

    @Override
    public List<String> getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        Array sqlArray = cs.getArray(columnIndex);
        return arrayToList(sqlArray);
    }

    private List<String> arrayToList(Array sqlArray) throws SQLException {
        if (sqlArray == null) {
            return Collections.emptyList(); // 或返回 null，根据业务需求
        }
        // getArray() 返回的是 Object，需要强转为 String[]
        String[] stringArray = (String[]) sqlArray.getArray();
        return Arrays.asList(stringArray);
    }
}