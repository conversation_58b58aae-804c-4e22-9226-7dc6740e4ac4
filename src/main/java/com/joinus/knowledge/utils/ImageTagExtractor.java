package com.joinus.knowledge.utils;

import com.joinus.knowledge.enums.OssEnum;
import com.joinus.knowledge.model.dto.ImageData;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class ImageTagExtractor {

    public static List<ImageData> extractImageData(String htmlString) {
        List<ImageData> ImageDataList = new ArrayList<>();

        // Regex to find <img> tags
        // It captures the whole img tag to then extract attributes from it
        Pattern imgTagPattern = Pattern.compile("<img[^>]*>");
        Matcher imgTagMatcher = imgTagPattern.matcher(htmlString);

        // Regex to find data-s3-enum attribute within an img tag
        Pattern enumPattern = Pattern.compile("data-s3-enum=\"([^\"]*)\"");
        // Regex to find data-s3-key attribute within an img tag
        Pattern keyPattern = Pattern.compile("data-s3-key=\"([^\"]*)\"");

        while (imgTagMatcher.find()) {
            String imgTag = imgTagMatcher.group(); // The full <img> tag

            String dataS3Enum = null;
            String dataS3Key = null;

            Matcher enumMatcher = enumPattern.matcher(imgTag);
            if (enumMatcher.find()) {
                dataS3Enum = enumMatcher.group(1); // Group 1 captures the attribute value
            }

            Matcher keyMatcher = keyPattern.matcher(imgTag);
            if (keyMatcher.find()) {
                dataS3Key = keyMatcher.group(1); // Group 1 captures the attribute value
            }

            if (dataS3Key != null) {
                // if data-s3-enum is null, set default
                if (dataS3Enum == null) {
                    dataS3Enum = OssEnum.MINIO_EDU_KNOWLEDGE_HUB.name();
                }
                ImageDataList.add(new ImageData(OssEnum.valueOf(dataS3Enum), dataS3Key));
            }
        }

        return ImageDataList;
    }

    public static void main(String[] args) {
        String htmlInput = "如图所示<img data-s3-enum=\"MINIO_EDU_KNOWLEDGE_HUB\" data-s3-key=\"3958c3d1-75e2-40f7-9f67-e5ea5756c228/250/c1fda529-f1aa-4602-8322-347c6aedc27a/76cd3f82-4c62-42e0-a44d-e91421f7a57e.png\" src=\"\"/>请给出答案<img data-s3-enum=\"MINIO_EDU_KNOWLEDGE_HUB\" data-s3-key=\"e505f07b-c512-4a19-97dc-38ee6ee990e5/19/2674a2e7-bded-4e58-931e-ddec5a19209b/d78a248b-986e-4237-a7e6-6976346771ea.png\" src=\"\"/>";
        String htmlInput2 = "Another example: <img src=\"someimage.jpg\" data-s3-key=\"another-key-123\" > and <img data-s3-enum=\"ALIYUN_EDU_KNOWLEDGE_HUB\" data-s3-key=\"key-only-no-src.png\">";


        List<ImageData> extractedData = extractImageData(htmlInput);
        System.out.println("--- Results for htmlInput ---");
        for (ImageData data : extractedData) {
            System.out.println(data);
        }

        List<ImageData> extractedData2 = extractImageData(htmlInput2);
        System.out.println("\n--- Results for htmlInput2 ---");
        for (ImageData data : extractedData2) {
            System.out.println(data);
        }

    }

    public static String buildImageTagHtml(List<ImageData> imageDatas) {
        StringBuilder sb = new StringBuilder();
        for (ImageData imageData : imageDatas) {
            sb.append(buildImageTagHtml(imageData));
        }
        return sb.toString();
    }

    public static String buildImageTagHtml(ImageData imageData) {
        return "<img data-s3-enum=\"" + imageData.getDataS3Enum().name() + "\" data-s3-key=\"" + imageData.getDataS3key() + "\" src=\"" + imageData.getSrc() + "\"/>";
    }

    public static String buildImageTagHtmlNoSrc(ImageData imageData) {
        return "<img data-s3-enum=\"" + imageData.getDataS3Enum().name() + "\" data-s3-key=\"" + imageData.getDataS3key() + "\" src=\"\"/>";
    }
}
