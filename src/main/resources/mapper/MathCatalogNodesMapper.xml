<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.knowledge.mapper.MathCatalogNodesMapper">

    <resultMap id="BaseResultMap" type="com.joinus.knowledge.model.entity.MathCatalogNodes">
            <id property="id" column="id" />
            <result property="name" column="name" />
            <result property="sortNo" column="sort_no" />
            <result property="parentId" column="parent_id" />
            <result property="idPath" column="id_path" />
            <result property="level" column="level" />
            <result property="textbookId" column="textbook_id" />
            <result property="createdAt" column="created_at" />
            <result property="updatedAt" column="updated_at" />
            <result property="deletedAt" column="deleted_at" />
            <result property="startPage" column="start_page" />
            <result property="endPage" column="end_page" />
    </resultMap>

    <sql id="Base_Column_List">
        id,name,sort_no,parent_id,id_path,level,
        textbook_id,created_at,updated_at,deleted_at,start_page,
        end_page
    </sql>
    <select id="list" resultType="com.joinus.knowledge.model.vo.MathCatalogNodeVO">
        select * from view_math_catalog_nodes
        <where>
            <if test="publisher != null and publisher != ''">
                and publisher = #{publisher}
            </if>
            <if test="grade != null">
                and grade = #{grade}
            </if>
            <if test="semester != null">
                and semester = #{semester}
            </if>
        </where>
    </select>
</mapper>
