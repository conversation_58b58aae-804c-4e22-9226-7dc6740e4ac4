<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.knowledge.mapper.MathKnowledgePointFilesMapper">

    <resultMap id="BaseResultMap" type="com.joinus.knowledge.model.entity.MathKnowledgePointFiles">
            <id property="knowledgePointId" column="knowledge_point_id" />
            <id property="fileId" column="file_id" />
            <result property="category" column="category" />
            <result property="sortNo" column="sort_no" />
    </resultMap>

    <sql id="Base_Column_List">
        knowledge_point_id,file_id,category,sort_no
    </sql>
</mapper>
