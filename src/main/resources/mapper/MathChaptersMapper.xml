<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.knowledge.mapper.MathChaptersMapper">

    <select id="list" resultType="com.joinus.knowledge.model.vo.MathChapterVO">
        select mc.id,
                mc.name,
                mc.sort_no as chapter_sort_no,
                te.grade,
                te.publisher,
                te.semester
            from math_catalog_nodes mc
            inner join math_textbooks te on mc.textbook_id = te.id and te.deleted_at is null
        where mc.deleted_at is null
            <if test="name != null and name != ''">
                and mc.name like concat('%', #{name}, '%')
            </if>
            <if test="grade != null">
                and te.grade = #{grade}
            </if>
            <if test="semester != null">
                and te.semester = #{semester}
            </if>
            <if test="publisher != null">
                and te.publisher = #{publisher.value}
            </if>
    </select>
</mapper>
